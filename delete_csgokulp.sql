-- First, find the user ID
SELECT id FROM users WHERE username = 'csgokulp';

-- Delete from user_roles table first (to handle foreign key constraints)
-- Replace X with the actual user ID from the previous query
DELETE FROM user_roles WHERE user_id = X;

-- Now delete the user
-- Replace X with the actual user ID from the previous query
DELETE FROM users WHERE username = 'csgokulp';

-- Verify the user has been deleted
SELECT * FROM users WHERE username = 'csgokulp';
