// Test Mobile API Endpoints
// Run this with: node test_mobile_api.js

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/mobile/v1';

// Test configuration
const testConfig = {
  staff_mobile: '9876543210', // Update with actual mobile from your staff table
  password: 'password123',    // Update with actual password
  device_info: {
    device_id: 'test_device_' + Date.now(),
    device_name: 'Test Device',
    platform: 'android',
    device_model: 'Test Model',
    os_version: 'Android 13',
    app_version: '1.0.0'
  }
};

// Helper function to make API requests
async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'x-platform': 'android',
        'x-app-version': '1.0.0',
        'x-device-id': testConfig.device_info.device_id,
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// Test functions
async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...');
  const result = await makeRequest('GET', '/health');
  
  if (result.success) {
    console.log('✅ Health check passed');
    console.log('📊 API Info:', result.data);
  } else {
    console.log('❌ Health check failed:', result.error);
  }
  
  return result.success;
}

async function testAPIInfo() {
  console.log('\n🔍 Testing API Info...');
  const result = await makeRequest('GET', '/');
  
  if (result.success) {
    console.log('✅ API info retrieved');
    console.log('📊 API Details:', result.data);
  } else {
    console.log('❌ API info failed:', result.error);
  }
  
  return result.success;
}

async function testStaffLogin() {
  console.log('\n🔍 Testing Staff Login...');
  const loginData = {
    staff_mobile: testConfig.staff_mobile,
    password: testConfig.password,
    device_info: testConfig.device_info
  };

  const result = await makeRequest('POST', '/auth/login', loginData);
  
  if (result.success) {
    console.log('✅ Login successful');
    console.log('👤 Staff Info:', result.data.staff);
    console.log('🔑 Tokens received');
    return result.data.tokens;
  } else {
    console.log('❌ Login failed:', result.error);
    console.log('💡 Make sure the staff mobile and password are correct');
    return null;
  }
}

async function testSendOTP() {
  console.log('\n🔍 Testing Send OTP...');
  const otpData = {
    staff_mobile: testConfig.staff_mobile
  };

  const result = await makeRequest('POST', '/auth/otp/send', otpData);
  
  if (result.success) {
    console.log('✅ OTP sent successfully');
    console.log('📱 Check console for development OTP');
  } else {
    console.log('❌ Send OTP failed:', result.error);
  }
  
  return result.success;
}

async function testGetProfile(accessToken) {
  console.log('\n🔍 Testing Get Profile...');
  
  if (!accessToken) {
    console.log('❌ No access token available');
    return false;
  }

  const result = await makeRequest('GET', '/auth/profile', null, {
    'Authorization': `Bearer ${accessToken}`
  });
  
  if (result.success) {
    console.log('✅ Profile retrieved successfully');
    console.log('👤 Profile Data:', result.data);
  } else {
    console.log('❌ Get profile failed:', result.error);
  }
  
  return result.success;
}

async function testRefreshToken(refreshToken) {
  console.log('\n🔍 Testing Token Refresh...');
  
  if (!refreshToken) {
    console.log('❌ No refresh token available');
    return false;
  }

  const result = await makeRequest('POST', '/auth/token/refresh', {
    refresh_token: refreshToken
  });
  
  if (result.success) {
    console.log('✅ Token refreshed successfully');
    console.log('🔑 New access token received');
    return result.data.tokens;
  } else {
    console.log('❌ Token refresh failed:', result.error);
    return null;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Mobile API Tests...');
  console.log('📱 Base URL:', BASE_URL);
  console.log('📞 Test Mobile:', testConfig.staff_mobile);
  
  let tokens = null;
  
  // Test basic endpoints
  await testHealthCheck();
  await testAPIInfo();
  
  // Test authentication
  tokens = await testStaffLogin();
  
  if (tokens) {
    // Test protected endpoints
    await testGetProfile(tokens.access_token);
    await testRefreshToken(tokens.refresh_token);
  }
  
  // Test OTP functionality
  await testSendOTP();
  
  console.log('\n✨ Tests completed!');
  console.log('\n💡 Tips:');
  console.log('- Make sure your API server is running on port 3000');
  console.log('- Update the staff_mobile and password in testConfig');
  console.log('- Check the database for device records after login');
  console.log('- Monitor the console for development OTP codes');
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  makeRequest,
  testHealthCheck,
  testStaffLogin,
  testGetProfile,
  runTests
};
