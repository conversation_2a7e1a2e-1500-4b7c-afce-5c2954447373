// src/models/masters/measurementunit.model.js
const db = require('../../config/database');

const MeasurementUnit = {
  // Get all measurement units
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT mu.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM measurement_units mu
      LEFT JOIN users u1 ON mu.created_by = u1.id
      LEFT JOIN users u2 ON mu.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE mu.is_active = 1';
    }
    
    query += ' ORDER BY mu.name ASC';
    
    return await db.query(query);
  },

  // Get measurement unit by ID
  findById: async (id) => {
    const query = `
      SELECT mu.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM measurement_units mu
      LEFT JOIN users u1 ON mu.created_by = u1.id
      LEFT JOIN users u2 ON mu.updated_by = u2.id
      WHERE mu.id = ?
    `;
    
    const measurementUnits = await db.query(query, [id]);
    return measurementUnits[0];
  },

  // Get measurement unit by name
  findByName: async (name) => {
    const query = 'SELECT * FROM measurement_units WHERE name = ?';
    const measurementUnits = await db.query(query, [name]);
    return measurementUnits[0];
  },

  // Get measurement unit by symbol
  findBySymbol: async (symbol) => {
    const query = 'SELECT * FROM measurement_units WHERE symbol = ?';
    const measurementUnits = await db.query(query, [symbol]);
    return measurementUnits[0];
  },

  // Create new measurement unit
  create: async (measurementUnitData) => {
    const { name, symbol, is_active = 1, created_by } = measurementUnitData;
    
    const query = `
      INSERT INTO measurement_units (name, symbol, is_active, created_by) 
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, symbol, is_active, created_by]);
    return { id: result.insertId, name, symbol, is_active, created_by };
  },

  // Update measurement unit
  update: async (id, measurementUnitData) => {
    const { name, symbol, is_active, updated_by } = measurementUnitData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (symbol !== undefined) {
      updates.push('symbol = ?');
      params.push(symbol);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE measurement_units SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...measurementUnitData };
  },

  // Delete measurement unit
  delete: async (id) => {
    const query = 'DELETE FROM measurement_units WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete measurement units
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid measurement unit IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM measurement_units WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle measurement unit active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE measurement_units SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = MeasurementUnit;
