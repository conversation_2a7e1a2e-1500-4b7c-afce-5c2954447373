// src/models/masters/designation.model.js
const db = require('../../config/database');

const Designation = {
  // Get all designations
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT d.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM designations d
      LEFT JOIN users u1 ON d.created_by = u1.id
      LEFT JOIN users u2 ON d.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE d.is_active = 1';
    }
    
    return await db.query(query);
  },

  // Get designation by ID
  findById: async (id) => {
    const query = `
      SELECT d.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM designations d
      LEFT JOIN users u1 ON d.created_by = u1.id
      LEFT JOIN users u2 ON d.updated_by = u2.id
      WHERE d.id = ?
    `;
    
    const designations = await db.query(query, [id]);
    return designations[0];
  },

  // Get designation by name
  findByName: async (name) => {
    const query = 'SELECT * FROM designations WHERE name = ?';
    const designations = await db.query(query, [name]);
    return designations[0];
  },

  // Get designation by code
  findByCode: async (code) => {
    const query = 'SELECT * FROM designations WHERE code = ?';
    const designations = await db.query(query, [code]);
    return designations[0];
  },

  // Create new designation
  create: async (designationData) => {
    const { name, code, is_active = 1, created_by } = designationData;
    
    const query = `
      INSERT INTO designations (name, code, is_active, created_by) 
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, code, is_active, created_by]);
    return { id: result.insertId, name, code, is_active, created_by };
  },

  // Update designation
  update: async (id, designationData) => {
    const { name, code, is_active, updated_by } = designationData;
    
    let query = 'UPDATE designations SET ';
    const params = [];
    
    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }
    
    if (code !== undefined) {
      query += 'code = ?, ';
      params.push(code);
    }
    
    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }
    
    query += 'updated_by = ? ';
    params.push(updated_by);
    
    query += 'WHERE id = ?';
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...designationData };
  },

  // Delete designation
  delete: async (id) => {
    const query = 'DELETE FROM designations WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete designations
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid designation IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM designations WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle designation active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE designations SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = Designation;
