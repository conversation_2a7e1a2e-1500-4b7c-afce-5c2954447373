// src/models/masters/projecttype.model.js
const db = require('../../config/database');

const ProjectType = {
  // Get all project types
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT pt.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM project_types pt
      LEFT JOIN users u1 ON pt.created_by = u1.id
      LEFT JOIN users u2 ON pt.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE pt.is_active = 1';
    }
    
    query += ' ORDER BY pt.id DESC';
    
    return await db.query(query);
  },

  // Get project type by ID
  findById: async (id) => {
    const query = `
      SELECT pt.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM project_types pt
      LEFT JOIN users u1 ON pt.created_by = u1.id
      LEFT JOIN users u2 ON pt.updated_by = u2.id
      WHERE pt.id = ?
    `;
    
    const projectTypes = await db.query(query, [id]);
    return projectTypes[0];
  },

  // Get project type by name
  findByName: async (name) => {
    const query = 'SELECT * FROM project_types WHERE name = ?';
    const projectTypes = await db.query(query, [name]);
    return projectTypes[0];
  },

  // Create new project type
  create: async (projectTypeData) => {
    const { name, is_active = 1, created_by } = projectTypeData;
    
    const query = `
      INSERT INTO project_types (name, is_active, created_by) 
      VALUES (?, ?, ?)
    `;
    
    const result = await db.query(query, [name, is_active, created_by]);
    return { id: result.insertId, name, is_active, created_by };
  },

  // Update project type
  update: async (id, projectTypeData) => {
    const { name, is_active, updated_by } = projectTypeData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE project_types SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...projectTypeData };
  },

  // Delete project type
  delete: async (id) => {
    const query = 'DELETE FROM project_types WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete project types
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid project type IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM project_types WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle project type active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE project_types SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = ProjectType;