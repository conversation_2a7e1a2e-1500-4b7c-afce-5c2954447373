// src/models/masters/city.model.js
const db = require('../../config/database');

const City = {
  // Get all cities
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT c.*, 
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM cities c
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE c.is_active = 1';
    }
    
    return await db.query(query);
  },

  // Get all cities by state
  findByState: async (stateId, includeInactive = false) => {
    let query = `
      SELECT c.*, 
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM cities c
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.updated_by = u2.id
      WHERE c.state_id = ?
    `;
    
    if (!includeInactive) {
      query += ' AND c.is_active = 1';
    }
    
    return await db.query(query, [stateId]);
  },

  // Get all cities by country
  findByCountry: async (countryId, includeInactive = false) => {
    let query = `
      SELECT c.*, 
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM cities c
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.updated_by = u2.id
      WHERE s.country_id = ?
    `;
    
    if (!includeInactive) {
      query += ' AND c.is_active = 1';
    }
    
    return await db.query(query, [countryId]);
  },

  // Get city by ID
  findById: async (id) => {
    const query = `
      SELECT c.*, 
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM cities c
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.updated_by = u2.id
      WHERE c.id = ?
    `;
    
    const cities = await db.query(query, [id]);
    return cities[0];
  },

  // Get city by name and state
  findByName: async (name, stateId) => {
    const query = 'SELECT * FROM cities WHERE name = ? AND state_id = ?';
    const cities = await db.query(query, [name, stateId]);
    return cities[0];
  },

  // Get city by code and state
  findByCode: async (code, stateId) => {
    const query = 'SELECT * FROM cities WHERE code = ? AND state_id = ?';
    const cities = await db.query(query, [code, stateId]);
    return cities[0];
  },

  // Create new city
  create: async (cityData) => {
    const { name, code, state_id, is_active = 1, created_by } = cityData;
    
    const query = `
      INSERT INTO cities (name, code, state_id, is_active, created_by) 
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, code, state_id, is_active, created_by]);
    return { id: result.insertId, name, code, state_id, is_active, created_by };
  },

  // Update city
  update: async (id, cityData) => {
    const { name, code, state_id, is_active, updated_by } = cityData;
    
    let query = 'UPDATE cities SET ';
    const params = [];
    
    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }
    
    if (code !== undefined) {
      query += 'code = ?, ';
      params.push(code);
    }
    
    if (state_id !== undefined) {
      query += 'state_id = ?, ';
      params.push(state_id);
    }
    
    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }
    
    query += 'updated_by = ? ';
    params.push(updated_by);
    
    query += 'WHERE id = ?';
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...cityData };
  },

  // Delete city
  delete: async (id) => {
    const query = 'DELETE FROM cities WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete cities
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid city IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM cities WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle city active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE cities SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = City;
