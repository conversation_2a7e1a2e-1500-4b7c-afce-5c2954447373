// src/models/masters/employmenttype.model.js
const db = require('../../config/database');

const EmploymentType = {
  // Get all employment types
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT et.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM employment_types et
      LEFT JOIN users u1 ON et.created_by = u1.id
      LEFT JOIN users u2 ON et.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE et.is_active = 1';
    }
    
    query += ' ORDER BY et.id DESC';
    
    return await db.query(query);
  },

  // Get employment type by ID
  findById: async (id) => {
    const query = `
      SELECT et.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM employment_types et
      LEFT JOIN users u1 ON et.created_by = u1.id
      LEFT JOIN users u2 ON et.updated_by = u2.id
      WHERE et.id = ?
    `;
    
    const employmentTypes = await db.query(query, [id]);
    return employmentTypes[0];
  },

  // Get employment type by name
  findByName: async (name) => {
    const query = 'SELECT * FROM employment_types WHERE name = ?';
    const employmentTypes = await db.query(query, [name]);
    return employmentTypes[0];
  },

  // Create new employment type
  create: async (employmentTypeData) => {
    const { name, is_active = 1, created_by } = employmentTypeData;
    
    const query = `
      INSERT INTO employment_types (name, is_active, created_by) 
      VALUES (?, ?, ?)
    `;
    
    const result = await db.query(query, [name, is_active, created_by]);
    return { id: result.insertId, name, is_active, created_by };
  },

  // Update employment type
  update: async (id, employmentTypeData) => {
    const { name, is_active, updated_by } = employmentTypeData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE employment_types SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...employmentTypeData };
  },

  // Delete employment type
  delete: async (id) => {
    const query = 'DELETE FROM employment_types WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete employment types
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid employment type IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM employment_types WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle employment type active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE employment_types SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = EmploymentType;