// src/models/staffDevice.model.js
const db = require('../config/database');

const StaffDevice = {
  /**
   * Register or update a device for a staff member
   */
  registerDevice: async (deviceData) => {
    const {
      staff_id,
      device_id,
      device_name,
      platform,
      device_model,
      os_version,
      app_version,
      push_token,
      user_agent,
      ip_address,
      location_info
    } = deviceData;

    // Check if device already exists
    const existingDevice = await StaffDevice.findByStaffAndDeviceId(staff_id, device_id);

    if (existingDevice) {
      // Update existing device
      const updateQuery = `
        UPDATE staff_devices 
        SET device_name = ?, platform = ?, device_model = ?, os_version = ?, 
            app_version = ?, push_token = ?, user_agent = ?, last_seen = NOW(),
            ip_address = ?, location_info = ?, updated_at = NOW()
        WHERE staff_id = ? AND device_id = ?
      `;
      
      await db.query(updateQuery, [
        device_name, platform, device_model, os_version, app_version,
        push_token, user_agent, ip_address, 
        location_info ? JSON.stringify(location_info) : null,
        staff_id, device_id
      ]);

      return await StaffDevice.findByStaffAndDeviceId(staff_id, device_id);
    } else {
      // Insert new device
      const insertQuery = `
        INSERT INTO staff_devices (
          staff_id, device_id, device_name, platform, device_model,
          os_version, app_version, push_token, user_agent, last_seen,
          ip_address, location_info, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)
      `;

      const result = await db.query(insertQuery, [
        staff_id, device_id, device_name, platform, device_model,
        os_version, app_version, push_token, user_agent, ip_address,
        location_info ? JSON.stringify(location_info) : null, staff_id
      ]);

      return await StaffDevice.findById(result.insertId);
    }
  },

  /**
   * Find device by staff ID and device ID
   */
  findByStaffAndDeviceId: async (staff_id, device_id) => {
    const query = `
      SELECT * FROM staff_devices 
      WHERE staff_id = ? AND device_id = ?
    `;
    
    const devices = await db.query(query, [staff_id, device_id]);
    return devices[0];
  },

  /**
   * Find device by ID
   */
  findById: async (id) => {
    const query = `
      SELECT sd.*, s.staff_name, s.staff_mobile
      FROM staff_devices sd
      LEFT JOIN staffs s ON sd.staff_id = s.id
      WHERE sd.id = ?
    `;
    
    const devices = await db.query(query, [id]);
    return devices[0];
  },

  /**
   * Get all devices for a staff member
   */
  findByStaffId: async (staff_id, includeInactive = false) => {
    let query = `
      SELECT * FROM staff_devices 
      WHERE staff_id = ?
    `;
    
    if (!includeInactive) {
      query += ' AND is_active = 1';
    }
    
    query += ' ORDER BY last_seen DESC';
    
    return await db.query(query, [staff_id]);
  },

  /**
   * Update device last seen timestamp
   */
  updateLastSeen: async (staff_id, device_id, ip_address = null) => {
    const query = `
      UPDATE staff_devices 
      SET last_seen = NOW(), ip_address = COALESCE(?, ip_address)
      WHERE staff_id = ? AND device_id = ?
    `;
    
    return await db.query(query, [ip_address, staff_id, device_id]);
  },

  /**
   * Update device login count
   */
  incrementLoginCount: async (staff_id, device_id) => {
    const query = `
      UPDATE staff_devices 
      SET login_count = login_count + 1, last_login = NOW()
      WHERE staff_id = ? AND device_id = ?
    `;
    
    return await db.query(query, [staff_id, device_id]);
  },

  /**
   * Deactivate a device
   */
  deactivateDevice: async (staff_id, device_id, updated_by = null) => {
    const query = `
      UPDATE staff_devices 
      SET is_active = 0, updated_by = ?, updated_at = NOW()
      WHERE staff_id = ? AND device_id = ?
    `;
    
    return await db.query(query, [updated_by, staff_id, device_id]);
  },

  /**
   * Mark device as trusted
   */
  markAsTrusted: async (staff_id, device_id, is_trusted = true, updated_by = null) => {
    const query = `
      UPDATE staff_devices 
      SET is_trusted = ?, updated_by = ?, updated_at = NOW()
      WHERE staff_id = ? AND device_id = ?
    `;
    
    return await db.query(query, [is_trusted ? 1 : 0, updated_by, staff_id, device_id]);
  },

  /**
   * Update push token
   */
  updatePushToken: async (staff_id, device_id, push_token) => {
    const query = `
      UPDATE staff_devices 
      SET push_token = ?, updated_at = NOW()
      WHERE staff_id = ? AND device_id = ?
    `;
    
    return await db.query(query, [push_token, staff_id, device_id]);
  },

  /**
   * Get device statistics for a staff member
   */
  getDeviceStats: async (staff_id) => {
    const query = `
      SELECT 
        COUNT(*) as total_devices,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_devices,
        COUNT(CASE WHEN is_trusted = 1 THEN 1 END) as trusted_devices,
        COUNT(CASE WHEN platform = 'android' THEN 1 END) as android_devices,
        COUNT(CASE WHEN platform = 'ios' THEN 1 END) as ios_devices,
        MAX(last_seen) as last_activity
      FROM staff_devices 
      WHERE staff_id = ?
    `;
    
    const stats = await db.query(query, [staff_id]);
    return stats[0];
  },

  /**
   * Clean up old inactive devices (older than specified days)
   */
  cleanupOldDevices: async (days = 90) => {
    const query = `
      DELETE FROM staff_devices 
      WHERE is_active = 0 
      AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;
    
    return await db.query(query, [days]);
  }
};

module.exports = StaffDevice;
