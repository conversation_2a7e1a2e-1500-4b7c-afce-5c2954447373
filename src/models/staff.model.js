const db = require('../config/database');
const bcrypt = require('bcrypt');

/**
 * Staff Model - Following the exact pattern of your working BloodGroup model
 */
const Staff = {
  /**
   * Get all staff - Following BloodGroup.findAll pattern
   */
  findAll: async (includeInactive = false) => {
    console.log('=== Staff.findAll - Simple Pattern ===');
    console.log('includeInactive:', includeInactive);

    let query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             et.name as employment_type_name,
             bg.name as blood_group_name,
             q.name as qualification_name,
             c.name as city_name,
             st.name as state_name,
             l.name as locality_name,
             r.name as role_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `;

    if (!includeInactive) {
      query += ' WHERE s.is_active = 1';
    }

    query += ' ORDER BY s.staff_name ASC';

    console.log('Query:', query);
    console.log('No parameters - following BloodGroup pattern');

    return await db.query(query);
  },

  /**
   * Get staff with pagination and filters - NEW METHOD
   */
  findAllWithFilters: async (options = {}) => {
    console.log('=== Staff.findAllWithFilters ===');
    console.log('Options:', JSON.stringify(options, null, 2));

    const {
      includeInactive = false,
      departmentId,
      designationId,
      search,
      limit,
      offset
    } = options;

    let query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             et.name as employment_type_name,
             bg.name as blood_group_name,
             q.name as qualification_name,
             c.name as city_name,
             st.name as state_name,
             l.name as locality_name,
             r.name as role_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `;

    const conditions = [];
    const params = [];

    // Active status filter
    if (!includeInactive) {
      conditions.push('s.is_active = ?');
      params.push(1);
    }

    // Department filter
    if (departmentId && !isNaN(parseInt(departmentId))) {
      conditions.push('s.department_id = ?');
      params.push(parseInt(departmentId));
    }

    // Designation filter
    if (designationId && !isNaN(parseInt(designationId))) {
      conditions.push('s.designation_id = ?');
      params.push(parseInt(designationId));
    }

    // Search filter
    if (search && typeof search === 'string' && search.trim().length > 0) {
      conditions.push('(s.staff_name LIKE ? OR s.staff_email LIKE ? OR s.staff_mobile LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Add WHERE clause
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Add ordering
    query += ' ORDER BY s.staff_name ASC';

    // Add pagination if provided
    if (limit && !isNaN(parseInt(limit)) && parseInt(limit) > 0) {
      // Use standard MySQL LIMIT offset, limit syntax
      if (offset !== undefined && !isNaN(parseInt(offset)) && parseInt(offset) > 0) {
        query += ` LIMIT ${parseInt(offset)}, ${parseInt(limit)}`;
      } else {
        query += ` LIMIT ${parseInt(limit)}`;
      }
    }

    console.log('Final Query:', query);
    console.log('Final Params:', params);
    console.log('Param count:', params.length);
    console.log('Placeholder count:', (query.match(/\?/g) || []).length);

    return await db.query(query, params);
  },

  /**
   * Get total count
   */
  getCount: async (options = {}) => {
    console.log('=== Staff.getCount ===');

    const {
      includeInactive = false,
      departmentId,
      designationId,
      search
    } = options;

    let query = 'SELECT COUNT(*) as total FROM staffs s';
    const conditions = [];
    const params = [];

    // Active status filter
    if (!includeInactive) {
      conditions.push('s.is_active = ?');
      params.push(1);
    }

    // Department filter
    if (departmentId && !isNaN(parseInt(departmentId))) {
      conditions.push('s.department_id = ?');
      params.push(parseInt(departmentId));
    }

    // Designation filter
    if (designationId && !isNaN(parseInt(designationId))) {
      conditions.push('s.designation_id = ?');
      params.push(parseInt(designationId));
    }

    // Search filter
    if (search && typeof search === 'string' && search.trim().length > 0) {
      conditions.push('(s.staff_name LIKE ? OR s.staff_email LIKE ? OR s.staff_mobile LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Add WHERE clause
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    console.log('Count Query:', query);
    console.log('Count Params:', params);

    const result = await db.query(query, params);
    return result[0].total;
  },

  /**
   * Get staff by ID - Following BloodGroup pattern
   */
  findById: async (id) => {
    const query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             et.name as employment_type_name,
             bg.name as blood_group_name,
             q.name as qualification_name,
             c.name as city_name,
             st.name as state_name,
             l.name as locality_name,
             r.name as role_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
      WHERE s.id = ?
    `;

    const staff = await db.query(query, [id]);
    return staff[0];
  },

  /**
   * Get staff by name - Following BloodGroup pattern
   */
  findByName: async (name) => {
    const query = 'SELECT * FROM staffs WHERE staff_name = ?';
    const staff = await db.query(query, [name]);
    return staff[0];
  },

  /**
   * Get staff by email - Following BloodGroup pattern
   */
  findByEmail: async (email) => {
    const query = 'SELECT * FROM staffs WHERE staff_email = ?';
    const staff = await db.query(query, [email]);
    return staff[0];
  },

  /**
   * Get staff by mobile - Following BloodGroup pattern
   */
  findByMobile: async (mobile) => {
    const query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      WHERE s.staff_mobile = ?
    `;
    const staff = await db.query(query, [mobile]);
    return staff[0];
  },

  /**
   * Get staff by Aadhaar - Following BloodGroup pattern
   */
  findByAadhaar: async (aadhaar) => {
    const query = 'SELECT * FROM staffs WHERE aadhaar_number = ?';
    const staff = await db.query(query, [aadhaar]);
    return staff[0];
  },

  /**
   * Get staff by PAN - Following BloodGroup pattern
   */
  findByPan: async (pan) => {
    const query = 'SELECT * FROM staffs WHERE pan_number = ?';
    const staff = await db.query(query, [pan]);
    return staff[0];
  },

  /**
   * Get staff by department
   */
  findByDepartment: async (departmentId, includeInactive = false) => {
    let query = `
      SELECT s.*, 
             d.name as designation_name, 
             dept.name as department_name,
             et.name as employment_type_name,
             bg.name as blood_group_name,
             q.name as qualification_name,
             c.name as city_name,
             st.name as state_name,
             l.name as locality_name,
             r.name as role_name
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      WHERE s.department_id = ?
    `;

    const params = [departmentId];

    if (!includeInactive) {
      query += ' AND s.is_active = ?';
      params.push(1);
    }

    query += ' ORDER BY s.staff_name ASC';

    return await db.query(query, params);
  },

  /**
   * Create new staff - Following BloodGroup pattern
   */
  create: async (staffData) => {
    console.log('=== Staff.create ===');
    console.log('Incoming staff data:', JSON.stringify(staffData, null, 2));

    const {
      staff_name,
      staff_mobile,
      staff_email,
      gender,
      user_role_id,
      date_of_birth,
      marital_status,
      blood_group_id,
      joining_date,
      emergency_contact_name,
      emergency_contact_phone,
      emergency_contact_relation,
      designation_id,
      employment_type_id,
      health_insurance_provider,
      health_insurance_number,
      department_id,
      salary_amount,
      salary_last_hiked_date,
      staff_address,
      locality_id,
      city_id,
      pincode,
      state_id,
      aadhaar_number,
      pan_number,
      qualification_id,
      bank_name,
      account_number,
      ifsc_code,
      profile_picture,
      password,
      is_active = 1,
      created_by
    } = staffData;

    // Hash password if provided
    let hashedPassword = null;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 10);
    }

    const query = `
      INSERT INTO staffs (
        staff_name, staff_mobile, staff_email, gender, user_role_id,
        date_of_birth, marital_status, blood_group_id, joining_date,
        emergency_contact_name, emergency_contact_phone, emergency_contact_relation,
        designation_id, employment_type_id, health_insurance_provider,
        health_insurance_number, department_id, salary_amount, salary_last_hiked_date,
        staff_address, locality_id, city_id, pincode, state_id,
        aadhaar_number, pan_number, qualification_id, bank_name,
        account_number, ifsc_code, profile_picture, password, is_active, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    // Convert undefined values to null for database compatibility
    const params = [
      staff_name || null,
      staff_mobile || null,
      staff_email || null,
      gender || null,
      user_role_id || null,
      date_of_birth || null,
      marital_status || null,
      blood_group_id || null,
      joining_date || null,
      emergency_contact_name || null,
      emergency_contact_phone || null,
      emergency_contact_relation || null,
      designation_id || null,
      employment_type_id || null,
      health_insurance_provider || null,
      health_insurance_number || null,
      department_id || null,
      salary_amount || null,
      salary_last_hiked_date || null,
      staff_address || null,
      locality_id || null,
      city_id || null,
      pincode || null,
      state_id || null,
      aadhaar_number || null,
      pan_number || null,
      qualification_id || null,
      bank_name || null,
      account_number || null,
      ifsc_code || null,
      profile_picture || null,
      hashedPassword,
      is_active,
      created_by
    ];

    console.log('Query:', query);
    console.log('Params count:', params.length);
    console.log('Params:', params);

    try {
      const result = await db.query(query, params);
      console.log('Insert result:', result);

      return { 
        id: result.insertId, 
        staff_name: staff_name || null, 
        staff_email: staff_email || null, 
        staff_mobile: staff_mobile || null, 
        is_active, 
        created_by 
      };
    } catch (error) {
      console.error('Database error in Staff.create:', error);
      throw error;
    }
  },

  /**
   * Update staff - Following BloodGroup pattern
   */
  update: async (id, staffData) => {
    console.log('=== Staff.update ===');
    console.log('Staff ID:', id);
    console.log('Update data:', JSON.stringify(staffData, null, 2));

    let query = 'UPDATE staffs SET ';
    const params = [];
    const updates = [];

    // Define allowed fields for update (exclude frontend-only fields)
    const allowedFields = [
      'staff_name', 'staff_email', 'staff_mobile', 'gender', 'date_of_birth',
      'marital_status', 'blood_group_id', 'user_role_id', 'joining_date',
      'designation_id', 'employment_type_id', 'department_id', 'salary_amount',
      'salary_last_hiked_date', 'emergency_contact_name', 'emergency_contact_phone',
      'emergency_contact_relation', 'staff_address', 'locality_id', 'city_id',
      'state_id', 'pincode', 'aadhaar_number', 'pan_number', 'qualification_id',
      'bank_name', 'account_number', 'ifsc_code', 'health_insurance_provider',
      'health_insurance_number', 'profile_picture', 'is_active', 'updated_by',
      'password', 'login_attempts', 'account_locked_until', 'last_login'
    ];

    // Build dynamic update query like BloodGroup
    Object.keys(staffData).forEach(key => {
      if (staffData[key] !== undefined && key !== 'id' && allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        params.push(staffData[key]);
      }
    });

    if (updates.length === 0) {
      throw new Error('No fields to update');
    }

    query += updates.join(', ') + ' WHERE id = ?';
    params.push(id);

    console.log('Update query:', query);
    console.log('Update params:', params);

    try {
      await db.query(query, params);
      return { id, ...staffData };
    } catch (error) {
      console.error('Database error in Staff.update:', error);
      throw error;
    }
  },

  /**
   * Delete staff - Following BloodGroup pattern
   */
  delete: async (id) => {
    console.log('=== Staff.delete ===');
    console.log('Deleting staff ID:', id);

    const query = 'DELETE FROM staffs WHERE id = ?';
    
    try {
      return await db.query(query, [id]);
    } catch (error) {
      console.error('Database error in Staff.delete:', error);
      throw error;
    }
  },

  /**
   * Bulk delete - Following BloodGroup pattern
   */
  bulkDelete: async (ids) => {
    console.log('=== Staff.bulkDelete ===');
    console.log('Deleting staff IDs:', ids);

    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid staff IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM staffs WHERE id IN (${placeholders})`;

    try {
      return await db.query(query, ids);
    } catch (error) {
      console.error('Database error in Staff.bulkDelete:', error);
      throw error;
    }
  },

  /**
   * Toggle active status - Following BloodGroup pattern
   */
  toggleActive: async (id, isActive, updatedBy) => {
    console.log('=== Staff.toggleActive ===');
    console.log('Staff ID:', id, 'Is Active:', isActive, 'Updated By:', updatedBy);

    const query = 'UPDATE staffs SET is_active = ?, updated_by = ? WHERE id = ?';
    
    try {
      await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
      return { id, is_active: isActive };
    } catch (error) {
      console.error('Database error in Staff.toggleActive:', error);
      throw error;
    }
  },

  /**
   * Update profile picture
   */
  updateProfilePicture: async (id, profilePicture, updatedBy) => {
    console.log('=== Staff.updateProfilePicture ===');
    console.log('Staff ID:', id);
    console.log('Profile picture path:', profilePicture);
    console.log('Updated by:', updatedBy);

    const query = 'UPDATE staffs SET profile_picture = ?, updated_by = ? WHERE id = ?';
    
    try {
      await db.query(query, [profilePicture, updatedBy, id]);
      return { id, profile_picture: profilePicture };
    } catch (error) {
      console.error('Database error in Staff.updateProfilePicture:', error);
      throw error;
    }
  },

  /**
   * Remove profile picture
   */
  removeProfilePicture: async (id, updatedBy) => {
    console.log('=== Staff.removeProfilePicture ===');
    console.log('Staff ID:', id);
    console.log('Updated by:', updatedBy);

    const query = 'UPDATE staffs SET profile_picture = NULL, updated_by = ? WHERE id = ?';
    
    try {
      await db.query(query, [updatedBy, id]);
      return { id, profile_picture: null };
    } catch (error) {
      console.error('Database error in Staff.removeProfilePicture:', error);
      throw error;
    }
  },

  /**
   * Bulk update status
   */
  bulkUpdateStatus: async (ids, isActive, updatedBy) => {
    console.log('=== Staff.bulkUpdateStatus ===');
    console.log('Staff IDs:', ids);
    console.log('Is Active:', isActive);
    console.log('Updated by:', updatedBy);

    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid staff IDs for bulk status update');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `UPDATE staffs SET is_active = ?, updated_by = ? WHERE id IN (${placeholders})`;
    const params = [isActive ? 1 : 0, updatedBy, ...ids];

    try {
      return await db.query(query, params);
    } catch (error) {
      console.error('Database error in Staff.bulkUpdateStatus:', error);
      throw error;
    }
  },

  /**
   * Find staff by multiple IDs
   */
  findByIds: async (ids) => {
    console.log('=== Staff.findByIds ===');
    console.log('Staff IDs:', ids);

    if (!Array.isArray(ids) || ids.length === 0) {
      return [];
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             et.name as employment_type_name,
             bg.name as blood_group_name,
             q.name as qualification_name,
             c.name as city_name,
             st.name as state_name,
             l.name as locality_name,
             r.name as role_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
      WHERE s.id IN (${placeholders})
      ORDER BY s.staff_name ASC
    `;

    try {
      return await db.query(query, ids);
    } catch (error) {
      console.error('Database error in Staff.findByIds:', error);
      throw error;
    }
  },

  /**
   * Search staff
   */
  search: async (searchTerm, limit = 10) => {
    console.log('=== Staff.search ===');
    console.log('Search term:', searchTerm);
    console.log('Limit:', limit);

    const query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             et.name as employment_type_name,
             bg.name as blood_group_name
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      WHERE s.is_active = 1
        AND (s.staff_name LIKE ?
             OR s.staff_email LIKE ?
             OR s.staff_mobile LIKE ?
             OR d.name LIKE ?
             OR dept.name LIKE ?)
      ORDER BY s.staff_name ASC
      LIMIT ?
    `;
    
    const searchPattern = `%${searchTerm}%`;
    const params = [
      searchPattern, searchPattern, searchPattern,
      searchPattern, searchPattern, limit
    ];

    try {
      return await db.query(query, params);
    } catch (error) {
      console.error('Database error in Staff.search:', error);
      throw error;
    }
  },

  /**
   * Get staff statistics
   */
  getStats: async () => {
    console.log('=== Staff.getStats ===');

    const queries = {
      total: 'SELECT COUNT(*) as count FROM staffs',
      active: 'SELECT COUNT(*) as count FROM staffs WHERE is_active = 1',
      inactive: 'SELECT COUNT(*) as count FROM staffs WHERE is_active = 0',
      byDepartment: `
        SELECT dept.name as department_name, COUNT(*) as count
        FROM staffs s
        LEFT JOIN departments dept ON s.department_id = dept.id
        WHERE s.is_active = 1
        GROUP BY s.department_id, dept.name
        ORDER BY count DESC
      `,
      recentJoins: `
        SELECT COUNT(*) as count
        FROM staffs
        WHERE joining_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      `
    };

    try {
      const [total, active, inactive, byDepartment, recentJoins] = await Promise.all([
        db.query(queries.total),
        db.query(queries.active),
        db.query(queries.inactive),
        db.query(queries.byDepartment),
        db.query(queries.recentJoins)
      ]);

      return {
        total: total[0]?.count || 0,
        active: active[0]?.count || 0,
        inactive: inactive[0]?.count || 0,
        byDepartment: byDepartment || [],
        recentJoins: recentJoins[0]?.count || 0
      };
    } catch (error) {
      console.error('Database error in Staff.getStats:', error);
      throw error;
    }
  },

  /**
   * Generate CSV for export
   */
  generateCSV: (staffData) => {
    console.log('=== Staff.generateCSV ===');
    console.log('Staff data count:', staffData ? staffData.length : 0);

    if (!staffData || staffData.length === 0) {
      return 'No data available';
    }

    // CSV headers
    const headers = [
      'ID', 'Name', 'Email', 'Mobile', 'Gender', 'Date of Birth',
      'Department', 'Designation', 'Employment Type', 'Joining Date',
      'Salary', 'Address', 'City', 'State', 'Pincode', 'Status'
    ];

    // Convert data to CSV rows
    const rows = staffData.map(staff => [
      staff.id,
      `"${staff.staff_name || ''}"`,
      `"${staff.staff_email || ''}"`,
      `"${staff.staff_mobile || ''}"`,
      `"${staff.gender || ''}"`,
      staff.date_of_birth || '',
      `"${staff.department_name || ''}"`,
      `"${staff.designation_name || ''}"`,
      `"${staff.employment_type_name || ''}"`,
      staff.joining_date || '',
      staff.salary_amount || '',
      `"${staff.staff_address || ''}"`,
      `"${staff.city_name || ''}"`,
      `"${staff.state_name || ''}"`,
      `"${staff.pincode || ''}"`,
      staff.is_active ? 'Active' : 'Inactive'
    ]);

    // Combine headers and rows
    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    return csvContent;
  },

  /**
   * Update staff password
   */
  updatePassword: async (id, newPassword, updatedBy) => {
    console.log('=== Staff.updatePassword ===');
    console.log('Staff ID:', id);
    console.log('Updated by:', updatedBy);

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    const query = `
      UPDATE staffs 
      SET password = ?, 
          updated_by = ?, 
          updated_at = NOW()
      WHERE id = ?
    `;

    const params = [hashedPassword, updatedBy, id];

    console.log('Query:', query);
    console.log('Params:', params);

    const result = await db.query(query, params);
    console.log('Update password result:', result);
    
    return result;
  }
};

module.exports = Staff;