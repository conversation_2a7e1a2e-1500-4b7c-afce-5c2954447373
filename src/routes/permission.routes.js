const express = require('express');
const permissionController = require('../controllers/auth/permission.controller');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all permission routes
router.use(verifyToken);

// Routes for managing permissions
router.get('/', 
  hasModulePermission('permission', 'read'), 
  permissionController.getAllPermissions
);

router.get('/:id', 
  hasModulePermission('permission', 'read'), 
  permissionController.getPermissionById
);

router.post('/', 
  hasModulePermission('permission', 'create'), 
  permissionController.createPermission
);

router.put('/:id', 
  hasModulePermission('permission', 'update'), 
  permissionController.updatePermission
);

router.delete('/:id', 
  hasModulePermission('permission', 'delete'), 
  permissionController.deletePermission
);

module.exports = router;
