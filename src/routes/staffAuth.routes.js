const express = require('express');
const router = express.Router();
const StaffAuthController = require('../controllers/staffAuth.controller');
const { staffAuthMiddleware } = require('../middleware/staffAuth.middleware');

/**
 * Staff Authentication Routes
 * These routes handle mobile app authentication for staff members
 */

// Public routes (no authentication required)

/**
 * @route   POST /api/staff/auth/login
 * @desc    Staff login with mobile and password
 * @access  Public
 */
router.post('/login', StaffAuthController.login);

/**
 * @route   POST /api/staff/auth/send-otp
 * @desc    Send OTP to staff mobile for login
 * @access  Public
 */
router.post('/send-otp', StaffAuthController.sendOTP);

/**
 * @route   POST /api/staff/auth/verify-otp
 * @desc    Verify OTP and login staff
 * @access  Public
 */
router.post('/verify-otp', StaffAuthController.verifyOTP);

/**
 * @route   POST /api/staff/auth/set-password
 * @desc    Set password for staff (first time setup)
 * @access  Public
 */
router.post('/set-password', StaffAuthController.setPassword);

/**
 * @route   POST /api/staff/auth/reset-password
 * @desc    Request password reset
 * @access  Public
 */
router.post('/reset-password', StaffAuthController.requestPasswordReset);

/**
 * @route   POST /api/staff/auth/verify-reset
 * @desc    Verify reset token and set new password
 * @access  Public
 */
router.post('/verify-reset', StaffAuthController.verifyPasswordReset);

/**
 * @route   POST /api/staff/auth/refresh-token
 * @desc    Refresh JWT access token
 * @access  Public
 */
router.post('/refresh-token', StaffAuthController.refreshToken);

// Protected routes (authentication required)

/**
 * @route   GET /api/staff/auth/profile
 * @desc    Get current staff profile
 * @access  Private (Staff)
 */
router.get('/profile', staffAuthMiddleware, async (req, res) => {
  try {
    const Staff = require('../models/staff.model');
    const staff = await Staff.findById(req.staff.id);

    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff profile not found'
      });
    }

    // Remove sensitive data
    const { password, password_reset_token, mobile_verification_token, ...staffData } = staff;

    res.json({
      success: true,
      data: staffData,
      message: 'Profile retrieved successfully'
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve profile'
    });
  }
});

/**
 * @route   PUT /api/staff/auth/profile
 * @desc    Update staff profile (limited fields)
 * @access  Private (Staff)
 */
router.put('/profile', staffAuthMiddleware, async (req, res) => {
  try {
    const Staff = require('../models/staff.model');
    const allowedFields = [
      'emergency_contact_name',
      'emergency_contact_phone',
      'emergency_contact_relation',
      'staff_address',
      'bank_name',
      'account_number',
      'ifsc_code'
    ];

    // Filter only allowed fields
    const updateData = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        updateData[key] = req.body[key];
      }
    });

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields to update'
      });
    }

    updateData.updated_by = req.staff.id;

    await Staff.update(req.staff.id, updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile'
    });
  }
});

/**
 * @route   POST /api/staff/auth/change-password
 * @desc    Change staff password
 * @access  Private (Staff)
 */
router.post('/change-password', staffAuthMiddleware, async (req, res) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = req.body;
    const bcrypt = require('bcrypt');
    const Staff = require('../models/staff.model');

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'All password fields are required'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'New passwords do not match'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 6 characters long'
      });
    }

    // Get current staff data
    const staff = await Staff.findById(req.staff.id);
    if (!staff || !staff.password) {
      return res.status(400).json({
        success: false,
        message: 'Current password not found'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, staff.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await Staff.update(req.staff.id, {
      password: hashedPassword,
      updated_by: req.staff.id
    });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change password'
    });
  }
});

/**
 * @route   POST /api/staff/auth/logout
 * @desc    Logout staff (client-side token removal)
 * @access  Private (Staff)
 */
router.post('/logout', staffAuthMiddleware, (req, res) => {
  // In a stateless JWT system, logout is handled client-side
  // by removing the token from storage
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

module.exports = router;
