// src/routes/user.routes.js

const express = require('express');
const userController = require('../controllers/auth/user.controller');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all user routes
router.use(verifyToken);

// Routes for managing users
router.get('/', hasModulePermission('user', 'read'), userController.getAllUsers);
router.get('/:id', hasModulePermission('user', 'read'), userController.getUserById);
router.post('/', hasModulePermission('user', 'create'), userController.createUser);
router.put('/:id', hasModulePermission('user', 'update'), userController.updateUser);
router.delete('/:id', hasModulePermission('user', 'delete'), userController.deleteUser);
router.patch('/:id/status', hasModulePermission('user', 'update'), userController.toggleUserStatus);
router.post('/:id/change-password', hasModulePermission('user', 'update'), userController.changePassword);

module.exports = router;