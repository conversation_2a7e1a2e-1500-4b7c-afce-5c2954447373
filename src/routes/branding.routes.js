const express = require('express');
const router = express.Router();
const BrandingController = require('../controllers/branding.controller');
const { verifyToken } = require('../middleware/auth.middleware');

/**
 * Admin Branding Routes
 * Base URL: /api/branding
 */

/**
 * Admin Key Middleware
 * Validates admin key for branding operations
 */
const validateAdminKey = (req, res, next) => {
  const adminKey = req.headers['x-admin-key'];
  
  if (!adminKey || adminKey !== process.env.ADMIN_API_KEY) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Admin access required'
    });
  }
  
  next();
};

/**
 * @route GET /api/branding
 * @desc Get current branding configuration
 * @access Private (Admin)
 */
router.get('/', verifyToken, BrandingController.getBranding);

/**
 * @route PUT /api/branding
 * @desc Update branding configuration
 * @access Private (Admin with key)
 * @body {
 *   appName: string,
 *   companyName: string,
 *   tagline?: string,
 *   theme?: {
 *     primaryColor?: string,
 *     secondaryColor?: string,
 *     accentColor?: string,
 *     errorColor?: string,
 *     backgroundColor?: string
 *   },
 *   assets?: {
 *     logoUrl?: string,
 *     appIconUrl?: string,
 *     splashImageUrl?: string
 *   },
 *   texts?: {
 *     loginWelcome?: string,
 *     loginSubtitle?: string,
 *     dashboardWelcome?: string,
 *     dashboardSubtitle?: string
 *   },
 *   features?: {
 *     showRegistration?: boolean,
 *     enableSocialLogin?: boolean,
 *     enableBiometric?: boolean,
 *     showForgotPassword?: boolean
 *   }
 * }
 */
router.put('/', verifyToken, validateAdminKey, BrandingController.updateBranding);

/**
 * @route POST /api/branding/reset
 * @desc Reset branding to default configuration
 * @access Private (Admin with key)
 */
router.post('/reset', verifyToken, validateAdminKey, BrandingController.resetToDefault);

module.exports = router;
