// src/routes/masters/country.routes.js
const express = require('express');
const countryController = require('../../controllers/masters/country.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing countries
router.get('/', hasModulePermission('masters', 'read'), countryController.getAllCountries);
router.get('/:id', hasModulePermission('masters', 'read'), countryController.getCountryById);
router.post('/', hasModulePermission('masters', 'create'), countryController.createCountry);
router.put('/:id', hasModulePermission('masters', 'update'), countryController.updateCountry);
router.delete('/:id', hasModulePermission('masters', 'delete'), countryController.deleteCountry);
router.patch('/:id/status', hasModulePermission('masters', 'update'), countryController.toggleCountryStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), countryController.bulkDeleteCountries);

module.exports = router;