// src/routes/masters/projectstatus.routes.js
const express = require('express');
const projectStatusController = require('../../controllers/masters/projectstatus.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing project statuses
router.get('/', hasModulePermission('masters', 'read'), projectStatusController.getAllProjectStatuses);
router.get('/:id', hasModulePermission('masters', 'read'), projectStatusController.getProjectStatusById);
router.post('/', hasModulePermission('masters', 'create'), projectStatusController.createProjectStatus);
router.put('/:id', hasModulePermission('masters', 'update'), projectStatusController.updateProjectStatus);
router.delete('/:id', hasModulePermission('masters', 'delete'), projectStatusController.deleteProjectStatus);
router.patch('/:id/status', hasModulePermission('masters', 'update'), projectStatusController.toggleProjectStatusStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), projectStatusController.bulkDeleteProjectStatuses);

module.exports = router;