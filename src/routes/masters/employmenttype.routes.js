// src/routes/masters/employmenttype.routes.js
const express = require('express');
const employmentTypeController = require('../../controllers/masters/employmenttype.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing employment types
router.get('/', hasModulePermission('masters', 'read'), employmentTypeController.getAllEmploymentTypes);
router.get('/:id', hasModulePermission('masters', 'read'), employmentTypeController.getEmploymentTypeById);
router.post('/', hasModulePermission('masters', 'create'), employmentTypeController.createEmploymentType);
router.put('/:id', hasModulePermission('masters', 'update'), employmentTypeController.updateEmploymentType);
router.delete('/:id', hasModulePermission('masters', 'delete'), employmentTypeController.deleteEmploymentType);
router.patch('/:id/status', hasModulePermission('masters', 'update'), employmentTypeController.toggleEmploymentTypeStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), employmentTypeController.bulkDeleteEmploymentTypes);

module.exports = router;