// src/routes/masters/productcategory.routes.js
const express = require('express');
const productCategoryController = require('../../controllers/masters/productcategory.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing product categories
router.get('/', hasModulePermission('masters', 'read'), productCategoryController.getAllProductCategories);
router.get('/:id', hasModulePermission('masters', 'read'), productCategoryController.getProductCategoryById);
router.post('/', hasModulePermission('masters', 'create'), productCategoryController.createProductCategory);
router.put('/:id', hasModulePermission('masters', 'update'), productCategoryController.updateProductCategory);
router.delete('/:id', hasModulePermission('masters', 'delete'), productCategoryController.deleteProductCategory);
router.patch('/:id/status', hasModulePermission('masters', 'update'), productCategoryController.toggleProductCategoryStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), productCategoryController.bulkDeleteProductCategories);

module.exports = router;