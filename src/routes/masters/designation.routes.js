// src/routes/masters/designation.routes.js
const express = require('express');
const designationController = require('../../controllers/masters/designation.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing designations
router.get('/', hasModulePermission('masters', 'read'), designationController.getAllDesignations);
router.get('/:id', hasModulePermission('masters', 'read'), designationController.getDesignationById);
router.post('/', hasModulePermission('masters', 'create'), designationController.createDesignation);
router.put('/:id', hasModulePermission('masters', 'update'), designationController.updateDesignation);
router.delete('/:id', hasModulePermission('masters', 'delete'), designationController.deleteDesignation);
router.patch('/:id/status', hasModulePermission('masters', 'update'), designationController.toggleDesignationStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), designationController.bulkDeleteDesignations);

module.exports = router;
