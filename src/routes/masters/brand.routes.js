// src/routes/masters/brand.routes.js
const express = require('express');
const brandController = require('../../controllers/masters/brand.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing brands
router.get('/', hasModulePermission('masters', 'read'), brandController.getAllBrands);
router.get('/subcategory/:subcategoryId', hasModulePermission('masters', 'read'), brandController.getBrandsBySubcategory);
router.get('/:id', hasModulePermission('masters', 'read'), brandController.getBrandById);
router.post('/', hasModulePermission('masters', 'create'), brandController.createBrand);
router.put('/:id', hasModulePermission('masters', 'update'), brandController.updateBrand);
router.delete('/:id', hasModulePermission('masters', 'delete'), brandController.deleteBrand);
router.patch('/:id/status', hasModulePermission('masters', 'update'), brandController.toggleBrandStatus);

module.exports = router;
