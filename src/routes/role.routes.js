const express = require('express');
const roleController = require('../controllers/auth/role.controller');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all role routes
router.use(verifyToken);

// Routes for managing roles
router.get('/', hasModulePermission('role', 'read'), roleController.getAllRoles);
router.get('/:id', hasModulePermission('role', 'read'), roleController.getRoleById);
router.post('/', hasModulePermission('role', 'create'), roleController.createRole);
router.put('/:id', hasModulePermission('role', 'update'), roleController.updateRole);
router.delete('/:id', hasModulePermission('role', 'delete'), roleController.deleteRole);
router.patch('/:id/status', hasModulePermission('role', 'update'), roleController.toggleRoleStatus);

// Routes for managing role permissions
router.post('/:roleId/permissions/:permissionId', hasModulePermission('role', 'update'), roleController.assignPermission);
router.delete('/:roleId/permissions/:permissionId', hasModulePermission('role', 'update'), roleController.removePermission);

// Routes for managing user roles
router.post('/users/:userId/:roleId', hasModulePermission('role', 'update'), roleController.assignRoleToUser);
router.delete('/users/:userId/:roleId', hasModulePermission('role', 'update'), roleController.removeRoleFromUser);
router.get('/:roleId/users', hasModulePermission('role', 'read'), roleController.getUsersWithRole);

module.exports = router;