const Role = require('../models/auth/role.model');
const Permission = require('../models/auth/permission.model');

class RoleService {
  // Get all roles
  static async getAllRoles(includeInactive = false) {
    return await Role.findAllWithCreator(includeInactive);
  }
  
  // Get role by ID with its permissions
  static async getRoleById(id, includeInactive = false) {
    const role = await Role.findByIdWithCreator(id);
    if (!role) {
      throw new Error('Role not found');
    }
    
    // If we don't want inactive roles and the role is inactive, throw error
    if (!includeInactive && !role.is_active) {
      throw new Error('Role is inactive');
    }
    
    const permissions = await Role.getRolePermissions(id);
    return {
      ...role,
      permissions
    };
  }
  
  // Create new role
  static async createRole(roleData, createdBy) {
    const existingRole = await Role.findByName(roleData.name);
    if (existingRole) {
      throw new Error('Role name already exists');
    }
    
    return await Role.create({
      ...roleData,
      created_by: createdBy
    });
  }
  
  // Update role
  static async updateRole(id, roleData) {
    const role = await Role.findById(id);
    if (!role) {
      throw new Error('Role not found');
    }
    
    if (roleData.name && roleData.name !== role.name) {
      const existingRole = await Role.findByName(roleData.name);
      if (existingRole) {
        throw new Error('Role name already exists');
      }
    }
    
    return await Role.update(id, roleData);
  }
  
  // Delete role
  static async deleteRole(id) {
    const role = await Role.findById(id);
    if (!role) {
      throw new Error('Role not found');
    }
    
    await Role.delete(id);
    return { success: true };
  }
  
  // Toggle role active status
  static async toggleRoleStatus(id, isActive) {
    const role = await Role.findById(id);
    if (!role) {
      throw new Error('Role not found');
    }
    
    return await Role.toggleActive(id, isActive);
  }
  
  // Assign permission to role
  static async assignPermission(roleId, permissionId) {
    const role = await Role.findById(roleId);
    if (!role) {
      throw new Error('Role not found');
    }
    
    const permission = await Permission.findById(permissionId);
    if (!permission) {
      throw new Error('Permission not found');
    }
    
    await Role.assignPermission(roleId, permissionId);
    return { success: true };
  }
  
  // Remove permission from role
  static async removePermission(roleId, permissionId) {
    const role = await Role.findById(roleId);
    if (!role) {
      throw new Error('Role not found');
    }
    
    const permission = await Permission.findById(permissionId);
    if (!permission) {
      throw new Error('Permission not found');
    }
    
    await Role.removePermission(roleId, permissionId);
    return { success: true };
  }
  
  // Assign role to user
  static async assignRoleToUser(userId, roleId) {
    const role = await Role.findById(roleId);
    if (!role) {
      throw new Error('Role not found');
    }
    
    if (!role.is_active) {
      throw new Error('Cannot assign inactive role');
    }
    
    await Role.assignToUser(userId, roleId);
    return { success: true };
  }
  
  // Remove role from user
  static async removeRoleFromUser(userId, roleId) {
    await Role.removeFromUser(userId, roleId);
    return { success: true };
  }
  
  // Get users with role
  static async getUsersWithRole(roleId) {
    const role = await Role.findById(roleId);
    if (!role) {
      throw new Error('Role not found');
    }
    
    return await Role.getUsersByRole(roleId);
  }
}

module.exports = RoleService;