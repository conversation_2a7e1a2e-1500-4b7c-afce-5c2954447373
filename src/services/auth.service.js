const jwt = require('jsonwebtoken');
const User = require('../models/auth/user.model');
const jwtConfig = require('../config/jwt');
const RoleModulePermission = require('../models/auth/role_module_permission.model');

class AuthService {
  static async login(identifier, password) {
    // Find the user by email or username
    const user = await User.findByEmailOrUsername(identifier);
    
    // If no user found or user is inactive
    if (!user) {
      throw new Error('Invalid credentials');
    }
    
    if (!user.is_active) {
      throw new Error('Account is disabled');
    }
    
    // Verify password
    const isPasswordValid = await User.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      throw new Error('Invalid credentials');
    }
    
    // Get user roles
    const roles = await User.getUserRoles(user.id);
    
    // Generate tokens
    return this.generateTokens(user, roles);
  }
  
  static async generateTokens(user, roles) {
    // Create payload with only user identity info
    const payload = {
      id: user.id,
      username: user.username,
      email: user.email,
      roles: roles.map(role => role.name)
    };
    
    // Generate access token
    const accessToken = jwt.sign(
      payload,
      jwtConfig.secret,
      { expiresIn: jwtConfig.expiresIn }
    );
    
    // Generate refresh token
    const refreshToken = jwt.sign(
      { id: user.id },
      jwtConfig.secret,
      { expiresIn: jwtConfig.refreshExpiresIn }
    );
    
    // Return tokens and basic user info without module permissions
    return {
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        roles: roles.map(role => role.name)
        // No modulePermissions here
      }
    };
  }
  
  static async refreshToken(token) {
    try {
      // Verify the refresh token
      const decoded = jwt.verify(token, jwtConfig.secret);
      
      // Get user data
      const user = await User.findById(decoded.id);
      if (!user || !user.is_active) {
        throw new Error('Invalid token');
      }
      
      // Get user roles
      const roles = await User.getUserRoles(user.id);
      
      // Generate new tokens
      return this.generateTokens(user, roles);
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  static async register(userData) {
    // Check if username or email already exists
    const existingEmail = await User.findByEmail(userData.email);
    if (existingEmail) {
      throw new Error('Email already in use');
    }
    
    const existingUsername = await User.findByUsername(userData.username);
    if (existingUsername) {
      throw new Error('Username already in use');
    }
    
    // Create the new user
    return await User.create(userData);
  }
}

module.exports = AuthService;