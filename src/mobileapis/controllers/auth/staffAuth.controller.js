const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const Staff = require('../../../models/staff.model');
const StaffDevice = require('../../../models/staffDevice.model');
const StaffDeviceSession = require('../../../models/staffDeviceSession.model');
const StaffDeviceLog = require('../../../models/staffDeviceLog.model');
const { sendOTPSMS, sendPasswordResetSMS } = require('../../../utils/sms.util');
const { mobileResponse } = require('../../utils/response.util');

/**
 * Mobile Staff Authentication Controller
 * Optimized for mobile app login and authentication
 */
const MobileStaffAuthController = {
  /**
   * Staff login with mobile and password
   */
  login: async (req, res) => {
    try {
      const { staff_mobile, password, device_info } = req.body;

      // Validation
      if (!staff_mobile || !password) {
        return mobileResponse.error(res, 'Mobile number and password are required', 400);
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        // Log failed login attempt
        await StaffDeviceLog.logLoginFailed(
          null, // No staff ID for non-existent user
          device_info?.device_id || req.deviceInfo?.deviceId || 'unknown',
          'Invalid mobile number',
          req.ip || req.connection.remoteAddress,
          req.headers['user-agent'],
          device_info?.platform || req.deviceInfo?.platform || 'unknown',
          device_info?.app_version || req.deviceInfo?.appVersion || '1.0.0'
        );
        return mobileResponse.error(res, 'Invalid mobile number or password', 401);
      }

      // Check if account is locked
      if (staff.account_locked_until && new Date() < new Date(staff.account_locked_until)) {
        return mobileResponse.error(res, 'Account is temporarily locked. Please try again later.', 423);
      }

      // Check if staff is active
      if (!staff.is_active) {
        return mobileResponse.error(res, 'Your account has been deactivated. Please contact administrator.', 401);
      }

      // Check if password is set
      if (!staff.password) {
        return mobileResponse.error(res, 'Password not set. Please use OTP login or contact administrator.', 400);
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, staff.password);
      if (!isPasswordValid) {
        // Increment login attempts
        const newAttempts = (staff.login_attempts || 0) + 1;
        const updateData = { login_attempts: newAttempts };

        // Lock account after 5 failed attempts for 30 minutes
        if (newAttempts >= 5) {
          updateData.account_locked_until = new Date(Date.now() + 30 * 60 * 1000);
        }

        await Staff.update(staff.id, updateData);
        return mobileResponse.error(res, 'Invalid mobile number or password', 401);
      }

      // Register/update device information
      let registeredDevice = null;
      try {
        const deviceInfo = req.deviceInfo || {};
        const deviceData = {
          staff_id: staff.id,
          device_id: device_info?.device_id || deviceInfo.deviceId || `unknown_${Date.now()}`,
          device_name: device_info?.device_name || `${deviceInfo.platform || 'Unknown'} Device`,
          platform: device_info?.platform || deviceInfo.platform || 'unknown',
          device_model: device_info?.device_model || deviceInfo.deviceModel || null,
          os_version: device_info?.os_version || deviceInfo.osVersion || null,
          app_version: device_info?.app_version || deviceInfo.appVersion || '1.0.0',
          push_token: device_info?.push_token || deviceInfo.pushToken || null,
          user_agent: req.headers['user-agent'] || deviceInfo.userAgent || null,
          ip_address: req.ip || req.connection.remoteAddress,
          location_info: device_info?.location || null
        };

        registeredDevice = await StaffDevice.registerDevice(deviceData);
        await StaffDevice.incrementLoginCount(staff.id, deviceData.device_id);
      } catch (deviceError) {
        console.error('Device registration error:', deviceError);
        // Continue without device registration if it fails
      }

      // Reset login attempts and update last login
      await Staff.update(staff.id, {
        login_attempts: 0,
        account_locked_until: null,
        last_login: new Date()
      });

      // Generate JWT tokens
      const accessToken = jwt.sign(
        {
          staffId: staff.id,
          mobile: staff.staff_mobile,
          deviceId: registeredDevice.id,
          type: 'mobile_staff'
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      const refreshToken = jwt.sign(
        {
          staffId: staff.id,
          deviceId: registeredDevice.id,
          type: 'mobile_staff_refresh'
        },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
      );

      // Create device session and log if device registration succeeded
      if (registeredDevice) {
        try {
          const sessionData = {
            staff_id: staff.id,
            device_id: registeredDevice.id,
            session_token: accessToken.substring(0, 50),
            refresh_token_hash: crypto.createHash('sha256').update(refreshToken).digest('hex'),
            expires_at: new Date(Date.now() + (24 * 60 * 60 * 1000)),
            ip_address: req.ip || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          };

          await StaffDeviceSession.createSession(sessionData);

          await StaffDeviceLog.logLoginSuccess(
            staff.id,
            registeredDevice.id,
            deviceData.device_id,
            sessionData.ip_address,
            sessionData.user_agent,
            deviceData.platform,
            deviceData.app_version
          );
        } catch (sessionError) {
          console.error('Session/logging error:', sessionError);
        }
      }

      // Get role and permissions for staff
      let roleData = null;
      let permissions = [];
      
      if (staff.user_role_id) {
        try {
          const Role = require('../../../models/auth/role.model');
          const roleInfo = await Role.findById(staff.user_role_id);
          if (roleInfo) {
            roleData = {
              id: roleInfo.id,
              name: roleInfo.name,
              description: roleInfo.description
            };
            
            // Get permissions for this role
            permissions = await Role.getRolePermissions(staff.user_role_id);
          }
        } catch (roleError) {
          console.error('Error fetching role information:', roleError);
        }
      }

      // Prepare mobile-optimized staff data
      const staffData = {
        id: staff.id,
        name: staff.staff_name,
        mobile: staff.staff_mobile,
        email: staff.staff_email,
        profile_picture: staff.profile_picture,
        department: {
          id: staff.department_id,
          name: staff.department_name
        },
        designation: {
          id: staff.designation_id,
          name: staff.designation_name
        },
        role: roleData,
        permissions: permissions.map(p => ({
          id: p.id,
          name: p.name,
          slug: p.slug,
          description: p.description
        })),
        device: registeredDevice ? {
          id: registeredDevice.id,
          name: registeredDevice.device_name,
          is_trusted: registeredDevice.is_trusted
        } : null
      };

      return mobileResponse.success(res, {
        staff: staffData,
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          token_type: 'Bearer',
          expires_in: process.env.JWT_EXPIRES_IN || '24h'
        }
      }, 'Login successful');

    } catch (error) {
      console.error('Mobile staff login error:', error);
      console.error('Error stack:', error.stack);
      return mobileResponse.error(res, `Login failed: ${error.message}`, 500);
    }
  },

  /**
   * Send OTP for mobile verification/login
   */
  sendOTP: async (req, res) => {
    try {
      const { staff_mobile } = req.body;

      if (!staff_mobile) {
        return mobileResponse.error(res, 'Mobile number is required', 400);
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return mobileResponse.error(res, 'Staff member not found with this mobile number', 404);
      }

      if (!staff.is_active) {
        return mobileResponse.error(res, 'Your account has been deactivated. Please contact administrator.', 401);
      }

      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Update staff with OTP
      await Staff.update(staff.id, {
        mobile_verification_token: otp,
        mobile_verification_expires: otpExpires
      });

      // Send SMS
      try {
        await sendOTPSMS(staff_mobile, otp, 10);
      } catch (smsError) {
        console.error('SMS sending failed:', smsError);
        // In development, log the OTP
        if (process.env.NODE_ENV === 'development') {
          console.log(`Development OTP for ${staff_mobile}: ${otp}`);
        }
      }

      return mobileResponse.success(res, {
        message: 'OTP sent successfully',
        expires_in: '10 minutes'
      }, 'OTP sent to your mobile number');

    } catch (error) {
      console.error('Send OTP error:', error);
      return mobileResponse.error(res, 'Failed to send OTP', 500);
    }
  },

  /**
   * Verify OTP and login
   */
  verifyOTP: async (req, res) => {
    try {
      const { staff_mobile, otp, device_info } = req.body;

      if (!staff_mobile || !otp) {
        return mobileResponse.error(res, 'Mobile number and OTP are required', 400);
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return mobileResponse.error(res, 'Staff member not found', 404);
      }

      // Check OTP
      if (!staff.mobile_verification_token || staff.mobile_verification_token !== otp) {
        return mobileResponse.error(res, 'Invalid OTP', 400);
      }

      // Check OTP expiry
      if (!staff.mobile_verification_expires || new Date() > new Date(staff.mobile_verification_expires)) {
        return mobileResponse.error(res, 'OTP has expired', 400);
      }

      // Clear OTP and mark mobile as verified
      await Staff.update(staff.id, {
        mobile_verification_token: null,
        mobile_verification_expires: null,
        mobile_verified: true,
        last_login: new Date()
      });

      // Generate JWT tokens
      const accessToken = jwt.sign(
        {
          staffId: staff.id,
          mobile: staff.staff_mobile,
          type: 'mobile_staff'
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      const refreshToken = jwt.sign(
        {
          staffId: staff.id,
          type: 'mobile_staff_refresh'
        },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
      );

      // Get role and permissions for staff
      let roleData = null;
      let permissions = [];
      
      if (staff.user_role_id) {
        try {
          const Role = require('../../../models/auth/role.model');
          const roleInfo = await Role.findById(staff.user_role_id);
          if (roleInfo) {
            roleData = {
              id: roleInfo.id,
              name: roleInfo.name,
              description: roleInfo.description
            };
            
            // Get permissions for this role
            permissions = await Role.getRolePermissions(staff.user_role_id);
          }
        } catch (roleError) {
          console.error('Error fetching role information:', roleError);
        }
      }

      // Prepare mobile-optimized staff data
      const staffData = {
        id: staff.id,
        name: staff.staff_name,
        mobile: staff.staff_mobile,
        email: staff.staff_email,
        profile_picture: staff.profile_picture,
        department: {
          id: staff.department_id,
          name: staff.department_name
        },
        designation: {
          id: staff.designation_id,
          name: staff.designation_name
        },
        role: roleData,
        permissions: permissions.map(p => ({
          id: p.id,
          name: p.name,
          slug: p.slug,
          description: p.description
        }))
      };

      return mobileResponse.success(res, {
        staff: staffData,
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: process.env.JWT_EXPIRES_IN || '24h'
        }
      }, 'OTP verified successfully');

    } catch (error) {
      console.error('Verify OTP error:', error);
      return mobileResponse.error(res, 'Failed to verify OTP', 500);
    }
  },

  /**
   * Set password for staff (first time setup)
   */
  setPassword: async (req, res) => {
    try {
      const { staff_mobile, password, confirm_password } = req.body;

      // Validation
      if (!staff_mobile || !password || !confirm_password) {
        return mobileResponse.error(res, 'All fields are required', 400);
      }

      if (password !== confirm_password) {
        return mobileResponse.error(res, 'Passwords do not match', 400);
      }

      if (password.length < 6) {
        return mobileResponse.error(res, 'Password must be at least 6 characters long', 400);
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return mobileResponse.error(res, 'Staff member not found', 404);
      }

      if (!staff.is_active) {
        return mobileResponse.error(res, 'Your account has been deactivated', 401);
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Update staff with hashed password
      await Staff.update(staff.id, {
        password: hashedPassword,
        mobile_verified: true
      });

      return mobileResponse.success(res, {
        message: 'Password set successfully'
      }, 'Password has been set successfully');

    } catch (error) {
      console.error('Set password error:', error);
      return mobileResponse.error(res, 'Failed to set password', 500);
    }
  },

  /**
   * Request password reset
   */
  requestPasswordReset: async (req, res) => {
    try {
      const { staff_mobile } = req.body;

      if (!staff_mobile) {
        return mobileResponse.error(res, 'Mobile number is required', 400);
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        // Don't reveal if mobile exists or not for security
        return mobileResponse.success(res, {
          message: 'Reset instructions sent'
        }, 'If the mobile number exists, reset instructions have been sent');
      }

      if (!staff.is_active) {
        return mobileResponse.error(res, 'Your account has been deactivated', 401);
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

      // Update staff with reset token
      await Staff.update(staff.id, {
        password_reset_token: resetToken,
        password_reset_expires: resetExpires
      });

      // Send SMS with reset code (first 8 characters)
      const resetCode = resetToken.substring(0, 8).toUpperCase();
      try {
        await sendPasswordResetSMS(staff_mobile, resetCode, 30);
      } catch (smsError) {
        console.error('SMS sending failed:', smsError);
        if (process.env.NODE_ENV === 'development') {
          console.log(`Development reset code for ${staff_mobile}: ${resetCode}`);
        }
      }

      return mobileResponse.success(res, {
        message: 'Reset code sent',
        expires_in: '30 minutes'
      }, 'Password reset code sent to your mobile number');

    } catch (error) {
      console.error('Password reset request error:', error);
      return mobileResponse.error(res, 'Failed to process password reset request', 500);
    }
  },

  /**
   * Verify reset code and set new password
   */
  verifyPasswordReset: async (req, res) => {
    try {
      const { staff_mobile, reset_code, new_password, confirm_password } = req.body;

      // Validation
      if (!staff_mobile || !reset_code || !new_password || !confirm_password) {
        return mobileResponse.error(res, 'All fields are required', 400);
      }

      if (new_password !== confirm_password) {
        return mobileResponse.error(res, 'Passwords do not match', 400);
      }

      if (new_password.length < 6) {
        return mobileResponse.error(res, 'Password must be at least 6 characters long', 400);
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return mobileResponse.error(res, 'Invalid reset request', 400);
      }

      // Verify reset code (check if provided code matches beginning of stored token)
      if (!staff.password_reset_token || !staff.password_reset_token.toLowerCase().startsWith(reset_code.toLowerCase())) {
        return mobileResponse.error(res, 'Invalid reset code', 400);
      }

      // Check token expiry
      if (!staff.password_reset_expires || new Date() > new Date(staff.password_reset_expires)) {
        return mobileResponse.error(res, 'Reset code has expired', 400);
      }

      // Hash new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(new_password, saltRounds);

      // Update staff with new password and clear reset token
      await Staff.update(staff.id, {
        password: hashedPassword,
        password_reset_token: null,
        password_reset_expires: null,
        login_attempts: 0,
        account_locked_until: null
      });

      return mobileResponse.success(res, {
        message: 'Password reset successful'
      }, 'Your password has been reset successfully');

    } catch (error) {
      console.error('Password reset verify error:', error);
      return mobileResponse.error(res, 'Failed to reset password', 500);
    }
  },

  /**
   * Refresh JWT token
   */
  refreshToken: async (req, res) => {
    try {
      const { refresh_token } = req.body;

      if (!refresh_token) {
        return mobileResponse.error(res, 'Refresh token is required', 401);
      }

      // Verify refresh token
      const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET);

      if (decoded.type !== 'mobile_staff_refresh') {
        return mobileResponse.error(res, 'Invalid token type', 401);
      }

      // Find staff
      const staff = await Staff.findById(decoded.staffId);
      if (!staff || !staff.is_active) {
        return mobileResponse.error(res, 'Staff not found or inactive', 401);
      }

      // Generate new access token
      const accessToken = jwt.sign(
        {
          staffId: staff.id,
          mobile: staff.staff_mobile,
          type: 'mobile_staff'
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      return mobileResponse.success(res, {
        tokens: {
          access_token: accessToken,
          expires_in: process.env.JWT_EXPIRES_IN || '24h'
        }
      }, 'Token refreshed successfully');

    } catch (error) {
      console.error('Token refresh error:', error);
      return mobileResponse.error(res, 'Invalid refresh token', 401);
    }
  },

  /**
   * Logout (client-side token removal)
   */
  logout: async (req, res) => {
    try {
      // In a stateless JWT system, logout is handled client-side
      // by removing the token from storage
      return mobileResponse.success(res, {
        message: 'Logged out successfully'
      }, 'You have been logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      return mobileResponse.error(res, 'Logout failed', 500);
    }
  }
};

module.exports = MobileStaffAuthController;