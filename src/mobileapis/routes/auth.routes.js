const express = require('express');
const router = express.Router();
const MobileStaffAuthController = require('../controllers/auth/staffAuth.controller');
const { 
  mobileAuthMiddleware, 
  rateLimitMiddleware, 
  deviceInfoMiddleware, 
  apiVersionMiddleware, 
  validateMobileInput 
} = require('../middleware/mobileAuth.middleware');

/**
 * Mobile Staff Authentication Routes
 * Base URL: /api/mobile/v1/auth/
 */

// Apply common middleware to all auth routes
router.use(deviceInfoMiddleware);
router.use(validateMobileInput);
router.use(apiVersionMiddleware('1.0.0')); // Minimum app version required

// Apply rate limiting to all auth routes (stricter for auth endpoints)
const authRateLimit = rateLimitMiddleware(50, 60 * 60 * 1000); // 50 requests per hour
router.use(authRateLimit);

// Public routes (no authentication required)

/**
 * @route   POST /api/mobile/v1/auth/login
 * @desc    Staff login with mobile number and password
 * @access  Public
 * @body    { staff_mobile, password, device_info }
 */
router.post('/login', MobileStaffAuthController.login);

/**
 * @route   POST /api/mobile/v1/auth/otp/send
 * @desc    Send OTP to staff mobile for verification/login
 * @access  Public
 * @body    { staff_mobile }
 */
router.post('/otp/send', MobileStaffAuthController.sendOTP);

/**
 * @route   POST /api/mobile/v1/auth/otp/verify
 * @desc    Verify OTP and login staff
 * @access  Public
 * @body    { staff_mobile, otp, device_info }
 */
router.post('/otp/verify', MobileStaffAuthController.verifyOTP);

/**
 * @route   POST /api/mobile/v1/auth/password/set
 * @desc    Set password for staff (first time setup or after OTP verification)
 * @access  Public
 * @body    { staff_mobile, password, confirm_password }
 */
router.post('/password/set', MobileStaffAuthController.setPassword);

/**
 * @route   POST /api/mobile/v1/auth/password/reset/request
 * @desc    Request password reset (sends reset code via SMS)
 * @access  Public
 * @body    { staff_mobile }
 */
router.post('/password/reset/request', MobileStaffAuthController.requestPasswordReset);

/**
 * @route   POST /api/mobile/v1/auth/password/reset/verify
 * @desc    Verify reset code and set new password
 * @access  Public
 * @body    { staff_mobile, reset_code, new_password, confirm_password }
 */
router.post('/password/reset/verify', MobileStaffAuthController.verifyPasswordReset);

/**
 * @route   POST /api/mobile/v1/auth/token/refresh
 * @desc    Refresh JWT access token using refresh token
 * @access  Public
 * @body    { refresh_token }
 */
router.post('/token/refresh', MobileStaffAuthController.refreshToken);

// Protected routes (authentication required)

/**
 * @route   POST /api/mobile/v1/auth/logout
 * @desc    Logout staff (invalidate tokens on client side)
 * @access  Private (Staff)
 */
router.post('/logout', mobileAuthMiddleware, MobileStaffAuthController.logout);

/**
 * @route   GET /api/mobile/v1/auth/profile
 * @desc    Get current staff profile information
 * @access  Private (Staff)
 */
router.get('/profile', mobileAuthMiddleware, async (req, res) => {
  try {
    const { mobileResponse } = require('../utils/response.util');
    const Staff = require('../../models/staff.model');
    
    const staff = await Staff.findById(req.staff.id);

    if (!staff) {
      return mobileResponse.notFoundError(res, 'Staff profile not found');
    }

    // Return mobile-optimized profile data
    const profileData = {
      id: staff.id,
      name: staff.staff_name,
      mobile: staff.staff_mobile,
      email: staff.staff_email,
      gender: staff.gender,
      profile_picture: staff.profile_picture,
      joining_date: staff.joining_date,
      department: {
        id: staff.department_id,
        name: staff.department_name
      },
      designation: {
        id: staff.designation_id,
        name: staff.designation_name
      },
      emergency_contact: {
        name: staff.emergency_contact_name,
        phone: staff.emergency_contact_phone,
        relation: staff.emergency_contact_relation
      },
      address: {
        street: staff.staff_address,
        city_id: staff.city_id,
        city_name: staff.city_name,
        state_id: staff.state_id,
        state_name: staff.state_name,
        pincode: staff.pincode
      },
      bank_details: {
        bank_name: staff.bank_name,
        account_number: staff.account_number ? '****' + staff.account_number.slice(-4) : null, // Mask account number
        ifsc_code: staff.ifsc_code
      },
      is_active: staff.is_active,
      last_login: staff.last_login
    };

    return mobileResponse.success(res, profileData, 'Profile retrieved successfully');

  } catch (error) {
    console.error('Get mobile profile error:', error);
    const { mobileResponse } = require('../utils/response.util');
    return mobileResponse.serverError(res, 'Failed to retrieve profile');
  }
});

/**
 * @route   PUT /api/mobile/v1/auth/profile
 * @desc    Update limited staff profile fields (mobile app)
 * @access  Private (Staff)
 * @body    { emergency_contact_name, emergency_contact_phone, emergency_contact_relation, staff_address, bank_name, account_number, ifsc_code }
 */
router.put('/profile', mobileAuthMiddleware, async (req, res) => {
  try {
    const { mobileResponse, validation } = require('../utils/response.util');
    const Staff = require('../../models/staff.model');
    
    // Define fields that staff can update via mobile app
    const allowedFields = [
      'emergency_contact_name',
      'emergency_contact_phone', 
      'emergency_contact_relation',
      'staff_address',
      'bank_name',
      'account_number',
      'ifsc_code'
    ];

    // Validate emergency contact phone if provided
    if (req.body.emergency_contact_phone && !validation.isValidMobile(req.body.emergency_contact_phone)) {
      return mobileResponse.validationError(res, {
        emergency_contact_phone: 'Invalid phone number format'
      });
    }

    // Filter only allowed fields
    const updateData = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key) && req.body[key] !== undefined) {
        updateData[key] = req.body[key];
      }
    });

    if (Object.keys(updateData).length === 0) {
      return mobileResponse.validationError(res, 'No valid fields to update');
    }

    updateData.updated_by = req.staff.id;

    await Staff.update(req.staff.id, updateData);

    return mobileResponse.success(res, {
      updated_fields: Object.keys(updateData).filter(key => key !== 'updated_by')
    }, 'Profile updated successfully');

  } catch (error) {
    console.error('Update mobile profile error:', error);
    const { mobileResponse } = require('../utils/response.util');
    return mobileResponse.serverError(res, 'Failed to update profile');
  }
});

/**
 * @route   POST /api/mobile/v1/auth/password/change
 * @desc    Change staff password (when logged in)
 * @access  Private (Staff)
 * @body    { current_password, new_password, confirm_password }
 */
router.post('/password/change', mobileAuthMiddleware, async (req, res) => {
  try {
    const { current_password, new_password, confirm_password } = req.body;
    const { mobileResponse, validation } = require('../utils/response.util');
    const bcrypt = require('bcrypt');
    const Staff = require('../../models/staff.model');

    // Validate required fields
    const missingFields = validation.checkRequiredFields(req.body, ['current_password', 'new_password', 'confirm_password']);
    if (missingFields.length > 0) {
      return mobileResponse.validationError(res, {
        missing_fields: missingFields
      });
    }

    if (new_password !== confirm_password) {
      return mobileResponse.validationError(res, {
        password: 'New passwords do not match'
      });
    }

    // Validate password strength
    const passwordValidation = validation.validatePassword(new_password);
    if (!passwordValidation.isValid) {
      return mobileResponse.validationError(res, {
        password: passwordValidation.errors
      });
    }

    // Get current staff data
    const staff = await Staff.findById(req.staff.id);
    if (!staff || !staff.password) {
      return mobileResponse.notFoundError(res, 'Current password not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(current_password, staff.password);
    if (!isCurrentPasswordValid) {
      return mobileResponse.validationError(res, {
        current_password: 'Current password is incorrect'
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(new_password, saltRounds);

    // Update password
    await Staff.update(req.staff.id, {
      password: hashedPassword,
      updated_by: req.staff.id
    });

    return mobileResponse.success(res, {
      message: 'Password changed successfully'
    }, 'Your password has been updated successfully');

  } catch (error) {
    console.error('Change password error:', error);
    const { mobileResponse } = require('../utils/response.util');
    return mobileResponse.serverError(res, 'Failed to change password');
  }
});

module.exports = router;