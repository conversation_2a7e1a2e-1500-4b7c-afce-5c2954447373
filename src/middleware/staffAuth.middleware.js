const jwt = require('jsonwebtoken');
const Staff = require('../models/staff.model');

/**
 * Staff Authentication Middleware
 * Verifies JWT tokens for staff mobile app access
 */
const staffAuthMiddleware = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if it's a staff token
    if (decoded.type !== 'staff') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token type'
      });
    }

    // Get staff details
    const staff = await Staff.findById(decoded.staffId);
    
    if (!staff) {
      return res.status(401).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    if (!staff.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account has been deactivated'
      });
    }

    // Add staff info to request object
    req.staff = {
      id: staff.id,
      mobile: staff.staff_mobile,
      name: staff.staff_name,
      email: staff.staff_email,
      department_id: staff.department_id,
      designation_id: staff.designation_id
    };

    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired'
      });
    }

    console.error('Staff auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

/**
 * Optional staff authentication middleware
 * Adds staff info if token is valid, but doesn't require authentication
 */
const optionalStaffAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type === 'staff') {
      const staff = await Staff.findById(decoded.staffId);
      
      if (staff && staff.is_active) {
        req.staff = {
          id: staff.id,
          mobile: staff.staff_mobile,
          name: staff.staff_name,
          email: staff.staff_email,
          department_id: staff.department_id,
          designation_id: staff.designation_id
        };
      }
    }

    next();

  } catch (error) {
    // Ignore errors and continue without authentication
    next();
  }
};

module.exports = {
  staffAuthMiddleware,
  optionalStaffAuth
};
