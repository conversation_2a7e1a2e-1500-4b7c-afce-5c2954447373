const db = require('../config/database');

/**
 * Admin Branding Controller
 * Handles branding configuration for admin panel
 */
class BrandingController {
  /**
   * Get current branding configuration
   * GET /api/branding
   */
  static async getBranding(req, res) {
    try {
      const query = `
        SELECT 
          id,
          app_name as appName,
          company_name as companyName,
          tagline,
          primary_color as primaryColor,
          secondary_color as secondaryColor,
          accent_color as accentColor,
          error_color as errorColor,
          background_color as backgroundColor,
          logo_url as logoUrl,
          app_icon_url as appIconUrl,
          splash_image_url as splashImageUrl,
          login_welcome as loginWelcome,
          login_subtitle as loginSubtitle,
          dashboard_welcome as dashboardWelcome,
          dashboard_subtitle as dashboardSubtitle,
          show_registration as showRegistration,
          enable_social_login as enableSocialLogin,
          enable_biometric as enableBiometric,
          show_forgot_password as showForgotPassword,
          version,
          is_active as isActive,
          created_at as createdAt,
          updated_at as updatedAt
        FROM app_branding 
        WHERE is_active = TRUE 
        ORDER BY id DESC 
        LIMIT 1
      `;

      const rows = await db.query(query);

      if (rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No branding configuration found'
        });
      }

      const branding = rows[0];

      return res.status(200).json({
        success: true,
        data: branding,
        message: 'Branding configuration retrieved successfully'
      });

    } catch (error) {
      console.error('Error fetching branding:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch branding configuration',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Update branding configuration (Admin only)
   * PUT /api/branding
   */
  static async updateBranding(req, res) {
    try {
      const {
        appName,
        companyName,
        tagline,
        theme,
        texts,
        features,
        assets
      } = req.body;

      // Validate required fields
      if (!appName || !companyName) {
        return res.status(400).json({
          success: false,
          message: 'App name and company name are required'
        });
      }

      // Start transaction
      const connection = await db.pool.getConnection();
      await connection.beginTransaction();

      try {
        // Deactivate current branding
        await connection.execute(
          'UPDATE app_branding SET is_active = FALSE WHERE is_active = TRUE'
        );

        // Get next version number
        const [versionRows] = await connection.execute(
          'SELECT MAX(version) as max_version FROM app_branding'
        );
        const nextVersion = (versionRows[0].max_version || 0) + 1;

        // Insert new branding
        const [result] = await connection.execute(
          `INSERT INTO app_branding (
            app_name, company_name, tagline,
            primary_color, secondary_color, accent_color, error_color, background_color,
            logo_url, app_icon_url, splash_image_url,
            login_welcome, login_subtitle, dashboard_welcome, dashboard_subtitle,
            show_registration, enable_social_login, enable_biometric, show_forgot_password,
            version, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            appName,
            companyName,
            tagline || null,
            theme?.primaryColor || '#667eea',
            theme?.secondaryColor || '#764ba2',
            theme?.accentColor || '#4CAF50',
            theme?.errorColor || '#FF5722',
            theme?.backgroundColor || '#F8FAFC',
            assets?.logoUrl || null,
            assets?.appIconUrl || null,
            assets?.splashImageUrl || null,
            texts?.loginWelcome || null,
            texts?.loginSubtitle || null,
            texts?.dashboardWelcome || null,
            texts?.dashboardSubtitle || null,
            features?.showRegistration || false,
            features?.enableSocialLogin || false,
            features?.enableBiometric || true,
            features?.showForgotPassword || true,
            nextVersion,
            true
          ]
        );

        await connection.commit();
        connection.release();

        // Return the updated branding
        const updatedBranding = {
          id: result.insertId,
          appName,
          companyName,
          tagline,
          primaryColor: theme?.primaryColor || '#667eea',
          secondaryColor: theme?.secondaryColor || '#764ba2',
          accentColor: theme?.accentColor || '#4CAF50',
          errorColor: theme?.errorColor || '#FF5722',
          backgroundColor: theme?.backgroundColor || '#F8FAFC',
          logoUrl: assets?.logoUrl || null,
          appIconUrl: assets?.appIconUrl || null,
          splashImageUrl: assets?.splashImageUrl || null,
          loginWelcome: texts?.loginWelcome || null,
          loginSubtitle: texts?.loginSubtitle || null,
          dashboardWelcome: texts?.dashboardWelcome || null,
          dashboardSubtitle: texts?.dashboardSubtitle || null,
          showRegistration: features?.showRegistration || false,
          enableSocialLogin: features?.enableSocialLogin || false,
          enableBiometric: features?.enableBiometric || true,
          showForgotPassword: features?.showForgotPassword || true,
          version: nextVersion,
          isActive: true,
          updatedAt: new Date().toISOString()
        };

        return res.status(200).json({
          success: true,
          data: updatedBranding,
          message: 'Branding configuration updated successfully'
        });

      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }

    } catch (error) {
      console.error('Error updating branding:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update branding configuration',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Reset branding to default configuration (Admin only)
   * POST /api/branding/reset
   */
  static async resetToDefault(req, res) {
    try {
      // Start transaction
      const connection = await db.pool.getConnection();
      await connection.beginTransaction();

      try {
        // Deactivate current branding
        await connection.execute(
          'UPDATE app_branding SET is_active = FALSE WHERE is_active = TRUE'
        );

        // Get next version number
        const [versionRows] = await connection.execute(
          'SELECT MAX(version) as max_version FROM app_branding'
        );
        const nextVersion = (versionRows[0].max_version || 0) + 1;

        // Insert default branding
        const [result] = await connection.execute(
          `INSERT INTO app_branding (
            app_name, company_name, tagline,
            primary_color, secondary_color, accent_color, error_color, background_color,
            logo_url, app_icon_url, splash_image_url,
            login_welcome, login_subtitle, dashboard_welcome, dashboard_subtitle,
            show_registration, enable_social_login, enable_biometric, show_forgot_password,
            version, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            'Plumeria Staff',
            'Plumeria Construction',
            'Construction Management Made Simple',
            '#667eea',
            '#764ba2',
            '#4CAF50',
            '#FF5722',
            '#F8FAFC',
            null,
            null,
            null,
            'Welcome to\nPlumeria Staff',
            'Construction Management Made Simple',
            'Welcome back,',
            'Have a productive day!',
            false,
            false,
            true,
            true,
            nextVersion,
            true
          ]
        );

        await connection.commit();
        connection.release();

        // Return the default branding configuration
        const defaultBranding = {
          id: result.insertId,
          appName: 'Plumeria Staff',
          companyName: 'Plumeria Construction',
          tagline: 'Construction Management Made Simple',
          primaryColor: '#667eea',
          secondaryColor: '#764ba2',
          accentColor: '#4CAF50',
          errorColor: '#FF5722',
          backgroundColor: '#F8FAFC',
          logoUrl: null,
          appIconUrl: null,
          splashImageUrl: null,
          loginWelcome: 'Welcome to\nPlumeria Staff',
          loginSubtitle: 'Construction Management Made Simple',
          dashboardWelcome: 'Welcome back,',
          dashboardSubtitle: 'Have a productive day!',
          showRegistration: false,
          enableSocialLogin: false,
          enableBiometric: true,
          showForgotPassword: true,
          version: nextVersion,
          isActive: true,
          updatedAt: new Date().toISOString()
        };

        return res.status(200).json({
          success: true,
          data: defaultBranding,
          message: 'Branding reset to default successfully'
        });

      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }

    } catch (error) {
      console.error('Error resetting branding:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to reset branding to default',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = BrandingController;
