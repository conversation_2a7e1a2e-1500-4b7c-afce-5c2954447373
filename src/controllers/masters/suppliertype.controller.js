// src/controllers/masters/suppliertype.controller.js
const SupplierType = require('../../models/masters/suppliertype.model');

// Get all supplier types
const getAllSupplierTypes = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const supplierTypes = await SupplierType.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: supplierTypes
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching supplier types',
      error: error.message
    });
  }
};

// Get supplier type by ID
const getSupplierTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const supplierType = await SupplierType.findById(id);
    
    if (!supplierType) {
      return res.status(404).json({
        success: false,
        message: 'Supplier type not found'
      });
    }
    
    return res.json({
      success: true,
      data: supplierType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching supplier type',
      error: error.message
    });
  }
};

// Create a new supplier type
const createSupplierType = async (req, res) => {
  try {
    const { name, description, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Supplier type name is required'
      });
    }
    
    // Check if supplier type name already exists
    const existingSupplierTypeWithName = await SupplierType.findByName(name);
    if (existingSupplierTypeWithName) {
      return res.status(409).json({
        success: false,
        message: 'Supplier type name already exists'
      });
    }
    
    // Create the supplier type with the authenticated user's ID
    const supplierType = await SupplierType.create({
      name,
      description,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Supplier type created successfully',
      data: supplierType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating supplier type',
      error: error.message
    });
  }
};

// Update a supplier type
const updateSupplierType = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, is_active } = req.body;
    
    // Check if supplier type exists
    const supplierType = await SupplierType.findById(id);
    if (!supplierType) {
      return res.status(404).json({
        success: false,
        message: 'Supplier type not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== supplierType.name) {
      const existingSupplierType = await SupplierType.findByName(name);
      if (existingSupplierType) {
        return res.status(409).json({
          success: false,
          message: 'Supplier type name already exists'
        });
      }
    }
    
    // Update the supplier type with the authenticated user's ID as the updater
    const updatedSupplierType = await SupplierType.update(id, {
      name,
      description,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Supplier type updated successfully',
      data: updatedSupplierType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating supplier type',
      error: error.message
    });
  }
};

// Delete a supplier type
const deleteSupplierType = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if supplier type exists
    const supplierType = await SupplierType.findById(id);
    if (!supplierType) {
      return res.status(404).json({
        success: false,
        message: 'Supplier type not found'
      });
    }
    
    // Delete the supplier type
    await SupplierType.delete(id);
    
    return res.json({
      success: true,
      message: 'Supplier type deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting supplier type',
      error: error.message
    });
  }
};

// Toggle supplier type active status
const toggleSupplierTypeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if supplier type exists
    const supplierType = await SupplierType.findById(id);
    if (!supplierType) {
      return res.status(404).json({
        success: false,
        message: 'Supplier type not found'
      });
    }
    
    // Toggle the status
    const updatedSupplierType = await SupplierType.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Supplier type ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedSupplierType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling supplier type status',
      error: error.message
    });
  }
};

// Bulk delete supplier types
const bulkDeleteSupplierTypes = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of supplier type IDs is required'
      });
    }
    
    // Perform bulk delete
    await SupplierType.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} supplier types`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllSupplierTypes,
  getSupplierTypeById,
  createSupplierType,
  updateSupplierType,
  deleteSupplierType,
  toggleSupplierTypeStatus,
  bulkDeleteSupplierTypes
};