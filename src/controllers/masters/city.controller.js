// src/controllers/masters/city.controller.js
const City = require('../../models/masters/city.model');
const State = require('../../models/masters/state.model');

// Get all cities
const getAllCities = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const stateId = req.query.stateId;
    const countryId = req.query.countryId;
    
    let cities;
    if (stateId) {
      // Get cities by state
      cities = await City.findByState(stateId, includeInactive);
    } else if (countryId) {
      // Get cities by country
      cities = await City.findByCountry(countryId, includeInactive);
    } else {
      // Get all cities
      cities = await City.findAll(includeInactive);
    }
    
    return res.json({
      success: true,
      data: cities
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching cities',
      error: error.message
    });
  }
};

// Get city by ID
const getCityById = async (req, res) => {
  try {
    const { id } = req.params;
    const city = await City.findById(id);
    
    if (!city) {
      return res.status(404).json({
        success: false,
        message: 'City not found'
      });
    }
    
    return res.json({
      success: true,
      data: city
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching city',
      error: error.message
    });
  }
};

// Create a new city
const createCity = async (req, res) => {
  try {
    const { name, code, state_id, is_active } = req.body;
    
    if (!name || !code || !state_id) {
      return res.status(400).json({
        success: false,
        message: 'City name, code, and state_id are required'
      });
    }
    
    // Check if state exists
    const state = await State.findById(state_id);
    if (!state) {
      return res.status(404).json({
        success: false,
        message: 'State not found'
      });
    }
    
    // Check if city name already exists for this state
    const existingCityWithName = await City.findByName(name, state_id);
    if (existingCityWithName) {
      return res.status(409).json({
        success: false,
        message: 'City name already exists for this state'
      });
    }
    
    // Check if city code already exists for this state
    const existingCityWithCode = await City.findByCode(code, state_id);
    if (existingCityWithCode) {
      return res.status(409).json({
        success: false,
        message: 'City code already exists for this state'
      });
    }
    
    // Create the city with the authenticated user's ID
    const city = await City.create({
      name,
      code,
      state_id,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'City created successfully',
      data: city
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating city',
      error: error.message
    });
  }
};

// Update a city
const updateCity = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, state_id, is_active } = req.body;
    
    // Check if city exists
    const city = await City.findById(id);
    if (!city) {
      return res.status(404).json({
        success: false,
        message: 'City not found'
      });
    }
    
    // If state_id is being changed, check if the new state exists
    if (state_id && state_id !== city.state_id) {
      const state = await State.findById(state_id);
      if (!state) {
        return res.status(404).json({
          success: false,
          message: 'State not found'
        });
      }
    }
    
    // Check if name is being changed and if it already exists for this state
    if (name && name !== city.name) {
      const stateIdToCheck = state_id || city.state_id;
      const existingCity = await City.findByName(name, stateIdToCheck);
      if (existingCity) {
        return res.status(409).json({
          success: false,
          message: 'City name already exists for this state'
        });
      }
    }
    
    // Check if code is being changed and if it already exists for this state
    if (code && code !== city.code) {
      const stateIdToCheck = state_id || city.state_id;
      const existingCity = await City.findByCode(code, stateIdToCheck);
      if (existingCity) {
        return res.status(409).json({
          success: false,
          message: 'City code already exists for this state'
        });
      }
    }
    
    // Update the city with the authenticated user's ID as the updater
    const updatedCity = await City.update(id, {
      name,
      code,
      state_id,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'City updated successfully',
      data: updatedCity
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating city',
      error: error.message
    });
  }
};

// Delete a city
const deleteCity = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if city exists
    const city = await City.findById(id);
    if (!city) {
      return res.status(404).json({
        success: false,
        message: 'City not found'
      });
    }
    
    // Delete the city
    await City.delete(id);
    
    return res.json({
      success: true,
      message: 'City deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting city',
      error: error.message
    });
  }
};

// Toggle city active status
const toggleCityStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if city exists
    const city = await City.findById(id);
    if (!city) {
      return res.status(404).json({
        success: false,
        message: 'City not found'
      });
    }
    
    // Toggle the status
    const updatedCity = await City.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `City ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedCity
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling city status',
      error: error.message
    });
  }
};

// Bulk delete cities
const bulkDeleteCities = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of city IDs is required'
      });
    }
    
    // Perform bulk delete
    await City.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} cities`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllCities,
  getCityById,
  createCity,
  updateCity,
  deleteCity,
  toggleCityStatus,
  bulkDeleteCities
};
