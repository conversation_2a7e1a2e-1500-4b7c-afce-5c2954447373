// src/controllers/masters/designation.controller.js
const Designation = require('../../models/masters/designation.model');

// Get all designations
const getAllDesignations = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const designations = await Designation.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: designations
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching designations',
      error: error.message
    });
  }
};

// Get designation by ID
const getDesignationById = async (req, res) => {
  try {
    const { id } = req.params;
    const designation = await Designation.findById(id);
    
    if (!designation) {
      return res.status(404).json({
        success: false,
        message: 'Designation not found'
      });
    }
    
    return res.json({
      success: true,
      data: designation
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching designation',
      error: error.message
    });
  }
};

// Create a new designation
const createDesignation = async (req, res) => {
  try {
    const { name, code, is_active } = req.body;
    
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Designation name and code are required'
      });
    }
    
    // Check if designation name already exists
    const existingDesignationWithName = await Designation.findByName(name);
    if (existingDesignationWithName) {
      return res.status(409).json({
        success: false,
        message: 'Designation name already exists'
      });
    }
    
    // Check if designation code already exists
    const existingDesignationWithCode = await Designation.findByCode(code);
    if (existingDesignationWithCode) {
      return res.status(409).json({
        success: false,
        message: 'Designation code already exists'
      });
    }
    
    // Create the designation with the authenticated user's ID
    const designation = await Designation.create({
      name,
      code,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Designation created successfully',
      data: designation
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating designation',
      error: error.message
    });
  }
};

// Update a designation
const updateDesignation = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, is_active } = req.body;
    
    // Check if designation exists
    const designation = await Designation.findById(id);
    if (!designation) {
      return res.status(404).json({
        success: false,
        message: 'Designation not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== designation.name) {
      const existingDesignation = await Designation.findByName(name);
      if (existingDesignation) {
        return res.status(409).json({
          success: false,
          message: 'Designation name already exists'
        });
      }
    }
    
    // Check if code is being changed and if it already exists
    if (code && code !== designation.code) {
      const existingDesignation = await Designation.findByCode(code);
      if (existingDesignation) {
        return res.status(409).json({
          success: false,
          message: 'Designation code already exists'
        });
      }
    }
    
    // Update the designation with the authenticated user's ID as the updater
    const updatedDesignation = await Designation.update(id, {
      name,
      code,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Designation updated successfully',
      data: updatedDesignation
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating designation',
      error: error.message
    });
  }
};

// Delete a designation
const deleteDesignation = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if designation exists
    const designation = await Designation.findById(id);
    if (!designation) {
      return res.status(404).json({
        success: false,
        message: 'Designation not found'
      });
    }
    
    // Delete the designation
    await Designation.delete(id);
    
    return res.json({
      success: true,
      message: 'Designation deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting designation',
      error: error.message
    });
  }
};

// Toggle designation active status
const toggleDesignationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if designation exists
    const designation = await Designation.findById(id);
    if (!designation) {
      return res.status(404).json({
        success: false,
        message: 'Designation not found'
      });
    }
    
    // Toggle the status
    const updatedDesignation = await Designation.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Designation ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedDesignation
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling designation status',
      error: error.message
    });
  }
};

// Bulk delete designations
const bulkDeleteDesignations = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of designation IDs is required'
      });
    }
    
    // Perform bulk delete
    await Designation.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} designations`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllDesignations,
  getDesignationById,
  createDesignation,
  updateDesignation,
  deleteDesignation,
  toggleDesignationStatus,
  bulkDeleteDesignations
};
