// src/controllers/masters/projectstatus.controller.js
const ProjectStatus = require('../../models/masters/projectstatus.model');

// Get all project statuses
const getAllProjectStatuses = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const projectStatuses = await ProjectStatus.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: projectStatuses
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching project statuses',
      error: error.message
    });
  }
};

// Get project status by ID
const getProjectStatusById = async (req, res) => {
  try {
    const { id } = req.params;
    const projectStatus = await ProjectStatus.findById(id);
    
    if (!projectStatus) {
      return res.status(404).json({
        success: false,
        message: 'Project status not found'
      });
    }
    
    return res.json({
      success: true,
      data: projectStatus
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching project status',
      error: error.message
    });
  }
};

// Create a new project status
const createProjectStatus = async (req, res) => {
  try {
    const { name, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Project status name is required'
      });
    }
    
    // Check if project status name already exists
    const existingProjectStatusWithName = await ProjectStatus.findByName(name);
    if (existingProjectStatusWithName) {
      return res.status(409).json({
        success: false,
        message: 'Project status name already exists'
      });
    }
    
    // Create the project status with the authenticated user's ID
    const projectStatus = await ProjectStatus.create({
      name,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Project status created successfully',
      data: projectStatus
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating project status',
      error: error.message
    });
  }
};

// Update a project status
const updateProjectStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, is_active } = req.body;
    
    // Check if project status exists
    const projectStatus = await ProjectStatus.findById(id);
    if (!projectStatus) {
      return res.status(404).json({
        success: false,
        message: 'Project status not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== projectStatus.name) {
      const existingProjectStatus = await ProjectStatus.findByName(name);
      if (existingProjectStatus) {
        return res.status(409).json({
          success: false,
          message: 'Project status name already exists'
        });
      }
    }
    
    // Update the project status with the authenticated user's ID as the updater
    const updatedProjectStatus = await ProjectStatus.update(id, {
      name,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Project status updated successfully',
      data: updatedProjectStatus
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating project status',
      error: error.message
    });
  }
};

// Delete a project status
const deleteProjectStatus = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if project status exists
    const projectStatus = await ProjectStatus.findById(id);
    if (!projectStatus) {
      return res.status(404).json({
        success: false,
        message: 'Project status not found'
      });
    }
    
    // Delete the project status
    await ProjectStatus.delete(id);
    
    return res.json({
      success: true,
      message: 'Project status deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting project status',
      error: error.message
    });
  }
};

// Toggle project status active status
const toggleProjectStatusStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if project status exists
    const projectStatus = await ProjectStatus.findById(id);
    if (!projectStatus) {
      return res.status(404).json({
        success: false,
        message: 'Project status not found'
      });
    }
    
    // Toggle the status
    const updatedProjectStatus = await ProjectStatus.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Project status ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedProjectStatus
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling project status status',
      error: error.message
    });
  }
};

// Bulk delete project statuses
const bulkDeleteProjectStatuses = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of project status IDs is required'
      });
    }
    
    // Perform bulk delete
    await ProjectStatus.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} project statuses`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllProjectStatuses,
  getProjectStatusById,
  createProjectStatus,
  updateProjectStatus,
  deleteProjectStatus,
  toggleProjectStatusStatus,
  bulkDeleteProjectStatuses
};