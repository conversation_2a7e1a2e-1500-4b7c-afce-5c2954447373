// src/controllers/masters/gstvalue.controller.js
const GstValue = require('../../models/masters/gstvalue.model');

// Get all GST values
const getAllGstValues = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const gstValues = await GstValue.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: gstValues
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching GST values',
      error: error.message
    });
  }
};

// Get GST value by ID
const getGstValueById = async (req, res) => {
  try {
    const { id } = req.params;
    const gstValue = await GstValue.findById(id);
    
    if (!gstValue) {
      return res.status(404).json({
        success: false,
        message: 'GST value not found'
      });
    }
    
    return res.json({
      success: true,
      data: gstValue
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching GST value',
      error: error.message
    });
  }
};

// Create a new GST value
const createGstValue = async (req, res) => {
  try {
    const { value, is_active } = req.body;
    
    if (value === undefined || value === null) {
      return res.status(400).json({
        success: false,
        message: 'GST value is required'
      });
    }
    
    // Validate that value is a number
    if (isNaN(value) || value < 0 || value > 100) {
      return res.status(400).json({
        success: false,
        message: 'GST value must be a number between 0 and 100'
      });
    }
    
    // Check if GST value already exists
    const existingGstValue = await GstValue.findByValue(value);
    if (existingGstValue) {
      return res.status(409).json({
        success: false,
        message: 'GST value already exists'
      });
    }
    
    // Create the GST value with the authenticated user's ID
    const gstValue = await GstValue.create({
      value,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'GST value created successfully',
      data: gstValue
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating GST value',
      error: error.message
    });
  }
};

// Update a GST value
const updateGstValue = async (req, res) => {
  try {
    const { id } = req.params;
    const { value, is_active } = req.body;
    
    // Check if GST value exists
    const gstValue = await GstValue.findById(id);
    if (!gstValue) {
      return res.status(404).json({
        success: false,
        message: 'GST value not found'
      });
    }
    
    // Validate value if provided
    if (value !== undefined && (isNaN(value) || value < 0 || value > 100)) {
      return res.status(400).json({
        success: false,
        message: 'GST value must be a number between 0 and 100'
      });
    }
    
    // Check if value is being changed and if it already exists
    if (value !== undefined && value !== gstValue.value) {
      const existingGstValue = await GstValue.findByValue(value);
      if (existingGstValue) {
        return res.status(409).json({
          success: false,
          message: 'GST value already exists'
        });
      }
    }
    
    // Update the GST value with the authenticated user's ID as the updater
    const updatedGstValue = await GstValue.update(id, {
      value,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'GST value updated successfully',
      data: updatedGstValue
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating GST value',
      error: error.message
    });
  }
};

// Delete a GST value
const deleteGstValue = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if GST value exists
    const gstValue = await GstValue.findById(id);
    if (!gstValue) {
      return res.status(404).json({
        success: false,
        message: 'GST value not found'
      });
    }
    
    // Delete the GST value
    await GstValue.delete(id);
    
    return res.json({
      success: true,
      message: 'GST value deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting GST value',
      error: error.message
    });
  }
};

// Toggle GST value active status
const toggleGstValueStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if GST value exists
    const gstValue = await GstValue.findById(id);
    if (!gstValue) {
      return res.status(404).json({
        success: false,
        message: 'GST value not found'
      });
    }
    
    // Toggle the status
    const updatedGstValue = await GstValue.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `GST value ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedGstValue
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling GST value status',
      error: error.message
    });
  }
};

// Bulk delete GST values
const bulkDeleteGstValues = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of GST value IDs is required'
      });
    }
    
    // Perform bulk delete
    await GstValue.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} GST values`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllGstValues,
  getGstValueById,
  createGstValue,
  updateGstValue,
  deleteGstValue,
  toggleGstValueStatus,
  bulkDeleteGstValues
};
