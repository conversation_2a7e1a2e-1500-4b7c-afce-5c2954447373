// src/controllers/masters/brand.controller.js
const Brand = require('../../models/masters/brand.model');

// Get all brands
const getAllBrands = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const brands = await Brand.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: brands
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching brands',
      error: error.message
    });
  }
};

// Get brand by ID
const getBrandById = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid brand ID is required'
      });
    }
    
    const brand = await Brand.findById(id);
    
    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }
    
    return res.json({
      success: true,
      data: brand
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching brand',
      error: error.message
    });
  }
};

// Get brands by product subcategory
const getBrandsBySubcategory = async (req, res) => {
  try {
    const { subcategoryId } = req.params;
    const includeInactive = req.query.includeInactive === 'true';
    
    if (!subcategoryId || isNaN(subcategoryId)) {
      return res.status(400).json({
        success: false,
        message: 'Valid product subcategory ID is required'
      });
    }
    
    const brands = await Brand.findBySubcategory(subcategoryId, includeInactive);
    
    return res.json({
      success: true,
      data: brands
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching brands by subcategory',
      error: error.message
    });
  }
};

// Create a new brand
const createBrand = async (req, res) => {
  try {
    const { name, product_subcategory_id, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Brand name is required'
      });
    }
    
    if (!product_subcategory_id) {
      return res.status(400).json({
        success: false,
        message: 'Product subcategory is required'
      });
    }
    
    // Check if brand name already exists in the same subcategory
    const existingBrand = await Brand.findByNameAndSubcategory(name, product_subcategory_id);
    if (existingBrand) {
      return res.status(409).json({
        success: false,
        message: 'Brand name already exists in this product subcategory'
      });
    }
    
    // Create the brand with the authenticated user's ID
    const brand = await Brand.create({
      name,
      product_subcategory_id,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Brand created successfully',
      data: brand
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating brand',
      error: error.message
    });
  }
};

// Update brand
const updateBrand = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, product_subcategory_id, is_active } = req.body;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid brand ID is required'
      });
    }
    
    // Check if brand exists
    const existingBrand = await Brand.findById(id);
    if (!existingBrand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }
    
    // Check if new name already exists in the same subcategory (excluding current brand)
    if (name && product_subcategory_id) {
      const duplicateBrand = await Brand.findByNameAndSubcategory(name, product_subcategory_id);
      if (duplicateBrand && duplicateBrand.id !== parseInt(id)) {
        return res.status(409).json({
          success: false,
          message: 'Brand name already exists in this product subcategory'
        });
      }
    }
    
    // Update the brand
    const updatedBrand = await Brand.update(id, {
      name,
      product_subcategory_id,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Brand updated successfully',
      data: updatedBrand
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating brand',
      error: error.message
    });
  }
};

// Delete brand (soft delete)
const deleteBrand = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid brand ID is required'
      });
    }
    
    // Check if brand exists
    const existingBrand = await Brand.findById(id);
    if (!existingBrand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }
    
    // Soft delete the brand
    await Brand.delete(id, req.user.id);
    
    return res.json({
      success: true,
      message: 'Brand deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting brand',
      error: error.message
    });
  }
};

// Toggle brand status
const toggleBrandStatus = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid brand ID is required'
      });
    }
    
    // Check if brand exists
    const existingBrand = await Brand.findById(id);
    if (!existingBrand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }
    
    // Toggle the status
    const updatedBrand = await Brand.toggleStatus(id, req.user.id);
    
    return res.json({
      success: true,
      message: `Brand ${updatedBrand.is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedBrand
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling brand status',
      error: error.message
    });
  }
};

module.exports = {
  getAllBrands,
  getBrandById,
  getBrandsBySubcategory,
  createBrand,
  updateBrand,
  deleteBrand,
  toggleBrandStatus
};
