const bcrypt = require('bcrypt');
const Staff = require('./src/models/staff.model');

async function testStaffLogin() {
  try {
    console.log('Testing staff login for mobile: 9843757161');
    
    const staff = await Staff.findByMobile('9843757161');
    
    if (!staff) {
      console.log('❌ Staff not found with mobile: 9843757161');
      console.log('Available staff:');
      const allStaff = await Staff.findAll();
      allStaff.forEach(s => {
        console.log(`- ID: ${s.id}, Mobile: ${s.staff_mobile}, Name: ${s.staff_name}`);
      });
      return;
    }
    
    console.log('✅ Staff found:');
    console.log(`- ID: ${staff.id}`);
    console.log(`- Name: ${staff.staff_name}`);
    console.log(`- Mobile: ${staff.staff_mobile}`);
    console.log(`- Email: ${staff.staff_email}`);
    console.log(`- Active: ${staff.is_active}`);
    console.log(`- Has Password: ${staff.password ? 'Yes' : 'No'}`);
    
    if (!staff.password) {
      console.log('❌ Staff has no password set');
      console.log('Setting password to "12345678"...');
      
      const hashedPassword = await bcrypt.hash('12345678', 12);
      await Staff.update(staff.id, { password: hashedPassword });
      
      console.log('✅ Password set successfully');
      return;
    }
    
    const isPasswordValid = await bcrypt.compare('12345678', staff.password);
    console.log(`Password valid: ${isPasswordValid ? 'Yes' : 'No'}`);
    
    if (!isPasswordValid) {
      console.log('❌ Password does not match');
      console.log('Updating password to "12345678"...');
      
      const hashedPassword = await bcrypt.hash('12345678', 12);
      await Staff.update(staff.id, { password: hashedPassword });
      
      console.log('✅ Password updated successfully');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testStaffLogin();
