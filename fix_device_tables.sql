-- Fix Device Tables and Add Test Data
-- First, let's check what staff records exist and create proper test data

-- Check existing staff records
SELECT id, staff_name, staff_mobile FROM staffs LIMIT 10;

-- If no staff records exist, let's create some test staff first
-- (Adjust the INSERT based on your actual staffs table structure)

-- Create test staff members if they don't exist
INSERT IGNORE INTO staffs (
  staff_name, staff_mobile, staff_email, gender, date_of_birth, 
  marital_status, joining_date, emergency_contact_name, 
  emergency_contact_phone, emergency_contact_relation, 
  designation_id, employment_type_id, department_id, 
  staff_address, locality_id, city_id, pincode, state_id, 
  is_active, created_by
) VALUES 
('<PERSON>', '9876543210', '<EMAIL>', 'Male', '1990-01-15', 
 'Single', '2024-01-01', '<PERSON>', '9876543211', 'Sister', 
 1, 1, 1, '123 Main Street', 1, 1, '123456', 1, 1, 1),
('<PERSON>', '9876543212', '<EMAIL>', 'Female', '1992-03-20', 
 'Married', '2024-02-01', '<PERSON>', '9876543213', 'Husband', 
 2, 1, 2, '456 Oak Avenue', 1, 1, '123456', 1, 1, 1),
('<PERSON>', '9876543214', 'mike.joh<PERSON>@company.com', 'Male', '1988-07-10', 
 'Single', '2024-03-01', '<PERSON> <PERSON>', '9876543215', '<PERSON>', 
 3, 1, 1, '789 <PERSON> Road', 1, 1, '123456', 1, 1, 1);

-- Now get the actual staff IDs that were created
SET @staff1_id = (SELECT id FROM staffs WHERE staff_mobile = '9876543210' LIMIT 1);
SET @staff2_id = (SELECT id FROM staffs WHERE staff_mobile = '9876543212' LIMIT 1);
SET @staff3_id = (SELECT id FROM staffs WHERE staff_mobile = '9876543214' LIMIT 1);

-- Clear existing device data that might have invalid references
DELETE FROM staff_device_logs;
DELETE FROM staff_device_sessions;
DELETE FROM staff_devices;

-- Insert device data with correct staff IDs
INSERT INTO staff_devices (
  staff_id, device_id, device_name, platform, device_model, 
  os_version, app_version, is_active, is_trusted, created_by
) VALUES 
(@staff1_id, 'device_android_001', 'John\'s Samsung Galaxy', 'android', 'Samsung Galaxy S23', 'Android 13', '1.0.0', TRUE, TRUE, @staff1_id),
(@staff1_id, 'device_ios_001', 'John\'s iPhone', 'ios', 'iPhone 14 Pro', 'iOS 16.5', '1.0.0', TRUE, FALSE, @staff1_id),
(@staff2_id, 'device_android_002', 'Jane\'s OnePlus', 'android', 'OnePlus 11', 'Android 13', '1.0.0', TRUE, TRUE, @staff2_id),
(@staff3_id, 'device_ios_002', 'Mike\'s iPad', 'ios', 'iPad Pro 12.9', 'iPadOS 16.5', '1.0.0', TRUE, FALSE, @staff3_id);

-- Get device IDs for session data
SET @device1_id = (SELECT id FROM staff_devices WHERE device_id = 'device_android_001' LIMIT 1);
SET @device2_id = (SELECT id FROM staff_devices WHERE device_id = 'device_android_002' LIMIT 1);

-- Insert sample session data with correct references
INSERT INTO staff_device_sessions (
  staff_id, device_id, session_token, login_time, expires_at, 
  ip_address, is_active
) VALUES 
(@staff1_id, @device1_id, 'session_token_001', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE),
(@staff2_id, @device2_id, 'session_token_002', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE);

-- Insert sample activity logs with correct references
INSERT INTO staff_device_logs (
  staff_id, device_id, device_identifier, event_type, event_description, 
  ip_address, platform, app_version
) VALUES 
(@staff1_id, @device1_id, 'device_android_001', 'login_success', 'Staff logged in successfully', '*************', 'android', '1.0.0'),
(@staff1_id, @device1_id, 'device_android_001', 'device_registered', 'New device registered for staff', '*************', 'android', '1.0.0'),
(@staff2_id, @device2_id, 'device_android_002', 'login_success', 'Staff logged in successfully', '*************', 'android', '1.0.0'),
(@staff3_id, NULL, 'unknown_device_123', 'login_failed', 'Failed login attempt - invalid credentials', '*************', 'android', '1.0.0');

-- Verify the data was inserted correctly
SELECT 'Staff Records:' as info;
SELECT id, staff_name, staff_mobile FROM staffs WHERE staff_mobile IN ('9876543210', '9876543212', '9876543214');

SELECT 'Device Records:' as info;
SELECT sd.id, sd.staff_id, sd.device_id, sd.device_name, s.staff_name 
FROM staff_devices sd 
JOIN staffs s ON sd.staff_id = s.id;

SELECT 'Session Records:' as info;
SELECT sds.id, sds.staff_id, sds.device_id, s.staff_name, sd.device_name
FROM staff_device_sessions sds
JOIN staffs s ON sds.staff_id = s.id
JOIN staff_devices sd ON sds.device_id = sd.id;

SELECT 'Log Records:' as info;
SELECT sdl.id, sdl.staff_id, sdl.event_type, s.staff_name
FROM staff_device_logs sdl
LEFT JOIN staffs s ON sdl.staff_id = s.id
LIMIT 10;
