const db = require('./src/config/database');

async function addSiteEngineerRole() {
  try {
    console.log('Adding Site Engineer role...');
    
    // Check if role already exists
    const existingRole = await db.query(
      'SELECT * FROM roles WHERE name = ?', 
      ['Site Engineer']
    );
    
    if (existingRole.length > 0) {
      console.log('Site Engineer role already exists!');
      console.log('Role details:', existingRole[0]);
      process.exit(0);
    }
    
    // Insert the new role
    const result = await db.query(
      'INSERT INTO roles (name, description, is_active, created_by) VALUES (?, ?, ?, ?)',
      [
        'Site Engineer',
        'Site engineer responsible for on-site construction activities and field operations',
        1,
        1  // Created by admin (user ID 1)
      ]
    );
    
    console.log('✅ Site Engineer role created successfully!');
    console.log('Role ID:', result.insertId);
    
    // Show all roles
    const allRoles = await db.query('SELECT * FROM roles ORDER BY id');
    console.log('\n📋 All roles in database:');
    console.table(allRoles);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error creating Site Engineer role:', error);
    process.exit(1);
  }
}

addSiteEngineerRole();